package com.facishare.open.qywx.accountinner.service;


import com.facishare.dubbo.plugin.annotation.RestAction;
import com.facishare.open.feishu.syncapi.model.ConnectInfoModel;
import com.facishare.open.qywx.accountinner.model.*;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto;
import com.facishare.open.qywx.accountsync.model.EnterpriseWechatUserModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.Result;

import java.util.List;

/**
 * 企业微信网关调用接口
 * Created by liu<PERSON> on 2018/07/18
 */
public interface QyweixinGatewayInnerService {

    /**
     * 处理企微第三方应用事件
     * @param eventProto
     * @return
     */
    String onMsgEvent(EnterpriseWeChatEventProto eventProto);

    /**
     * 处理第三方应用会话消息
     * @param eventProto
     * @return
     */
    String onDataEvent(EnterpriseWeChatEventProto eventProto);

    /**
     * 处理企业代开发应用事件
     * @param eventProto
     * @return
     */
    String onRepEvent(EnterpriseWeChatEventProto eventProto);

    /**
     * 处理系统事件
     * @param eventProto
     * @return
     */
    String onSystemData(EnterpriseWeChatEventProto eventProto);


    /**
     * 接收企业微信消息事件
     * 这里有很多事件。包括ticket接收，授权成功/变更通知，通讯录变更通知
     * @param plainMsg
     * @return
     */
    String recvMsgEvent(String plainMsg);

    /**
     * 接收企业微信 用户在应用会话session中上行的消息。
     * @param plainMsg
     * @return
     */
    String recvDataEvent(String plainMsg,String appId);

    /**
     * 接收企业微信代开发应用事件。
     *
     * 这里有很多事件。包括ticket接收，授权成功，更改secret
     **/
    String repMsgEvent(String plainMsg);

    /**
     * 发送企业微信用户信息MQ
     * @param model
     */
    void sendEnterpriseWechatUserInfo(EnterpriseWechatUserModel model);


    String getSuiteAccessToken(String appId);

    /**
     * 获取预授权码
     *
     * @param appId
     * @return
     */
    String getPreAuthCode(String appId);

    /**
     * 调用公费电话
     *
     * @param caller
     * @param authCorpId
     *
     * @return
     */
    Result<String> doDial(String caller, String authCorpId);

    /**
     * 处理官网应用授权业务
     * 1. 保存永久授权码
     * 2. 封装ticket
     * @param authCode
     * @param appId
     * @param fsEa
     * @return
     */
    Result<String> getAuthInfoDoInitCorp(String authCode, String appId, String fsEa);

    String getRegisterCode(String templateId);

    Result<QyweixinLoginInfoRsp> code2WebLoginUserInfo(String code);

    /**
     *
     * @param code
     * @param appId
     * @param outEa 只有代开发应用才支持，三方应用传null
     * @return
     */
    Result<Object> code2AppLoginUserInfo(String code, String appId, String outEa);

    Result<QyweixinRepUserDetailInfoRsp> getRepAppLoginUserInfo(String appId, String corpId, String code);

    /**
     * 处理登录授权信息
     * @param authCode
     * @return
     */
    @RestAction("loginAuth2")
    Result<CorpTicketResult> loginAuth(String authCode, String state,String fsEa);
    Result<CorpTicketResult> loginAuth(QyweixinLoginInfoRsp loginInfo, String state,String fsEa);

    /**
     * 企业微信内应用登录 OAuth 回调
     * @param code
     * @param state
     * @return
     */
    @RestAction("appAuth2")
    Result<CorpTicketResult> appAuth(String code, String state,String fsEa);
    Result<CorpTicketResult> appAuth(Object userInfo,String appId,String fsEa);

    /**
     * 生成纷享ticket
     * @param appId
     * @param corpId
     * @param userId
     * @param fsEa
     * @return
     */
    Result<String> genFsTicket(String appId, String corpId, String userId, String fsEa);

    String sendMessageTest();

    /**
     * 获取企业微信平台上，一个企业对应用的授权信息。
     * @param appID: 企业微信开放平台分配的appid
     * @param corpId :企业微信平台分配给企业的账号
     * @return : @see   QywxCorpAuthorizeInfo
     * */
    Result<QywxCorpAuthorizeInfo> getQywxCorpAuthorizeInfo(String appID, String corpId);

    Result<QywxAccessTokenInfo> getAccessTokenInfo(String fsEnterpriseAccount, String appId);

    Result<QywxAccessTokenInfo> getAccessTokenInfo2(String corpId, String appId);

    /**
     * 小程序 第三方登录凭证校验
     * @param code
     * @param appId
     * @return
     */
    Result<String> jscode2sessionService(String code, String appId);

    Result<String> miniprogramEncryptData(String ticket, String encryptedData, String iv);

    String getRedirectLoginUrl(String ticket, String appId,String corpId,String fromOrigin);

    void notifyHistoryToDoMessageGateway(List<String> eaList);

    /**
     * 重新发送订单支付成功消息
     * @param appId
     * @param orderId
     */
    void payForAppSuccessEvent(String appId, String orderId);

    /**
     * 删除部门
     * @param fsEa
     * @param outEa
     * @param appId
     * @param outDepartmentId
     * @return
     */
//    void deleteDepartment(String fsEa, String outEa,String appId,String outDepartmentId);

    /**
     * 根据企业微信企业ID绑定的纷享企业所在的云环境，获取对应的纷享企业所在的云环境的域名，比如https://www.fxiaoke.com,https://crm.unicloudea.com/
     * @param corpId 企业微信企业ID
     * @return
     */
    String getDomainByCorpId(String corpId);

//    /**
//     * 代开发授权账号绑定
//     */
//    Result<Void> saveCorpInfo(String ea, String corpId, String corpName);

//    /**
//     * CRM企业和企业微信企业绑定，支持换绑
//     * @param fsEa
//     * @param corpId 明文的corpId，需要转换成密文的
//     * @param depId 一级部门ID，企微一对多使用
//     * @param changeBind 是否换绑，用当前ea和corpId绑定
//     * @param dataCenterId 集成平台企业微信连接器ID
//     * @return
//     */
//    Result<Void> qywxCorpBind(String fsEa, String corpId, String depId,boolean changeBind, String dataCenterId);

    Result<String> getExternalUserId(String isvExternalUserId);

    /**
     * 获取外部联系人详情
     * @param fsEa
     * @param externalUserId
     * @return
     */
    @Deprecated
    Result<QyweixinExternalContactRsp> getExternalContactDetail(String fsEa, String externalUserId);
    Result<QyweixinExternalContactRsp> getExternalContactDetail2(String fsEa, String externalUserId, String outEa);

    /**
     * 获取客户群列表
     * @param groupChatInfo
     * @return
     */
    Result<QyweixinGroupChatResult> getGroupChat(QyweixinGroupChatInfo groupChatInfo);

    /**
     * 获取客户群列表
     * @param groupChatInfo
     * @return
     */
    Result<GroupChatListResult> getGroupChatList(QyweixinGroupChatInfo groupChatInfo);

    /**
     * 获取客户群详情
     * @param fsEa
     * @param chatId
     * @return
     */
    @Deprecated
    Result<QyweixinGroupChatDetail> getGroupChatDetail(String fsEa, String chatId,String appId);
    Result<QyweixinGroupChatDetail> getGroupChatDetail2(String fsEa, String chatId,String appId, String outEa);

    /**
     * 获取会话存档开启成员列表
     * @param fsEa
     * @return
     */
    @Deprecated
    Result<GetPermitUserListResult> getPermitUserList(String fsEa);
    Result<GetPermitUserListResult> getPermitUserList2(String fsEa, String outEa);

    /**
     * 获取会话存档开启成员列表
     * @param fsEa
     * @return
     */
    @Deprecated
    Result<CheckRoomAgreeResult> checkRoomAgree(String fsEa, String roomId);
    Result<CheckRoomAgreeResult> checkRoomAgree2(String fsEa, String roomId, String outEa);

    /**
     * 企业可通过下述接口，获取会话中外部成员的同意情况
     * @param arg
     * @return
     */
    Result<CheckSingleAgreeResult> checkSingleAgree(QyweixinCheckSingleAgreeArg arg);

    /**
     * 查看授权企业的信息
     * @param corpId
     * @param appId
     * @return
     */
    Result<CorpInfoModel> getCorpInfo(String corpId, String appId);

    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> getUserInfo(String corpId, String appId, String userId);

    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> getUserInfoByPhone(String corpId, String appId, String phone);

    Result<QyweixinCorpBindBo> getCorpBindInfo(String corpId, String appId);

    /**
     * 查询企微连接器连接信息
     * @param dataCenterId 数据中心ID
     * @return
     */
    Result<QYWXConnectParam> queryConnectInfo(String fsEa, String dataCenterId);

    /**
     * 更新企微连接信息
     * @param connectParam
     * @return
     */
    Result<Void> fsBindWithQywx(QYWXConnectParam connectParam,boolean checkRepApp);
    Result<Void> fsUnBindWithQywx(QYWXConnectParam connectParam);
    Result<Void> fsUnBindWithQywx2(String outEa, String outDepId);
    /**
     * 检查并初始化企微连接器，仅供集成平台调用
     * @param fsEa
     * @return
     */
    Result<Void> checkAndInitConnector(String fsEa, String dataCenterId);
    /**
     * 获取登录用户身份，仅用于登录授权应用
     * @param code
     * @return
     */
    Result<QyweixinUserInfo3rdRsp> getUserInfoByLoginAuthApp(String code);
    /**
     * 获取代开发应用appId
     * @return
     */
    Result<String> getValidRepAppId(String outEa);

    /**
     * 获取主应用appId，第一个正常状态的应用就是主应用
     * @return
     */
    Result<String> getMainAppId(String outEa);

    /**
     * 获取企业安装的代开发应用
     * @param outEa
     * @return
     */
    Result<List<QyweixinCorpBindBo>> getRepAppList(String outEa);
}
