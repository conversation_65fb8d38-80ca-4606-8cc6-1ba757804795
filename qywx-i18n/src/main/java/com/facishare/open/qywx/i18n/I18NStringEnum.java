package com.facishare.open.qywx.i18n;

import com.fxiaoke.i18n.client.I18nClient;
import com.github.trace.TraceContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

/**
 * 国际化词条枚举类
 * <AUTHOR>
 * @date 2023-10-16
 */
@SuppressWarnings("SpellCheckingInspection")
@Getter
@ToString
@AllArgsConstructor
public enum I18NStringEnum {
    s1("成功"),
    s2("失败"),
    s3("调用函数失败"),
    s4("内部错误"),
    s5("参数错误"),
    s6("查询数据为空"),
    s7("员工账号绑定失败"),
    s8("找不到此ticket的信息"),
    s9("ticket已经过期"),
    s10("查不到企业的授权信息"),
    s11("绑定数量为0"),
    s12("企业初始化中"),
    s13("该企业微信账号已绑定其它纷享账号，请纷享客服"),
    s14("不存在绑定关系（引导安装通讯录应用）"),
    s15("通讯录首次绑定"),
    s16("该纷享账号已绑定其它企业微信账号，请纷享客服解绑"),
    s17("CRM应用没有安装"),
    s18("手动绑定场景，请在CRM系统企业微信对接页面->帐号绑定->已绑定Tab查看对应人员是否已正确绑定，如果没有绑定，请手动添加绑定"),
    s19("被拨打用户安装应用时未授权拨打公费电话权限"),
    s20("呼叫号码不支持"),
    s21("公费电话余额不足"),
    s22("被叫号码不存在"),
    s23("管理员收到的服务商公费电话个数超过限制"),
    s24("其他公费电话返回错误"),
    s25("缺少caller参数"),
    s26("缺少callee参数"),
    s27("缺少auth_corpid参数"),
    s28("超过拨打公费电话频率，同一个客服5秒内只能调用api拨打一次公费电话"),
    s29("呼叫号码不支持"),
    s30("号码非法"),
    s31("不存在外部联系人的关系"),
    s32("未开启公费电话应用"),
    s33("caller不存在"),
    s34("caller跟callee电话号码一致"),
    s35("服务商拨打次数超过限制，单个企业管理员，在一天（以上午10:00为起始时间）内，对应单个服务商，只能被呼叫【4】次。"),
    s36("不合法的外部联系人授权码，非法或者已经消费过"),
    s37("应用未配置客服"),
    s38("客服userid不在应用配置的客服列表中"),
    s39("没有外部联系人权限"),
    s40("通讯录同步已取消"),
    s41("不能解绑企业开通管理员"),
    s42("从企业微信端添加应用生成的账号不允许绑定"),
    s43("企业不在授权绑定的白名单中"),
    s44("没有需要转译的内容"),
    s45("手动绑定不支持"),
    s46("获取外部联系人为空"),
    s47("自动绑定失败"),
    s48("获取access token failed"),
    s49("获取企业微信用户信息失败"),
    s50("获取登录ticket失败"),
    s51("员工已停用，请在CRM应用可见范围删除再添加有问题的人员"),
    s52("代开发应用未安装或者代开发应用未授权"),
    s53("代开发应用已停用，请删除代开发应用并重新授权安装"),
    s54("CRM应用未安装，请重新安装"),
    s55("CRM应用已停用，请删除CRM应用并重新安装"),
    s56("CRM企业已经和另一个企业微信企业绑定，已绑定的企业微信ID=%s，企业微信名称=%s，请解绑后再操作！"),
    s57("企业微信企业已经和另一个CRM企业绑定，已绑定的CRM企业ea=%s，请解绑后再操作！"),
    s58("企业微信企业ID转换失败"),
    s59("文件大小超过20M"),
    s60("获取文件元数据失败"),
    s61("文件下载失败"),
    s62("文件上传失败"),
    s63("纷享企业没有和企业微信企业绑定"),
    s64("暂时还没有数据"),
    s65("企微客户没有安装有效的纷享企微应用，比如CRM，订货通，服务通等"),
    s66("企微和CRM绑定失败，请检查是否输错了企微部门id"),
    s67("找不企微企业和纷享企业的绑定关系"),
    s68("纷享员工创建失败"),
    s69("企微员工绑定关系不存在"),
    s70("纷享企业管理员不能停用"),
    s71("非代开发应用和代开发应用的可见范围内同时有相同的标签，只以代开发应用的身份处理标签更新事件"),
    s72("请检查是否有参数未填写"),
    s73("获取token失败"),
    s74("获取企业微信签名失败"),
    s75("crm部门停用失败"),
    s76("crm部门启用失败"),
    s77("在职离职继承失败"),
    s78("图片大小超过10M"),
    s79("视频大小超过10M"),
    s80("语音大小超过2M"),
    s81("查询纷享员工信息失败"),
    s82("连接信息为空"),
    s83("纷享和企微企业绑定失败"),
    s84("企微连接器初始化失败"),
    s85("企微连接器订单下单失败"),
    s86("如果要使用企微连接器，请购买"),
    s87("手动绑定的企业，企微连接器已经初始化"),
    s88("当前绑定的企微企业已经和别的纷享企业存在自动绑定关系，请先解绑原自动绑定关系，再执行反绑定操作"),
    s89("导出中..."),
    s90("数据列表不可为空"),
    s91("NPATH文件下载失败"),
    s92("指定的成员/部门/标签参数无权限"),
    s93("贵企业总账号已达上限"),
    s94("解绑失败"),
    s95("非法调用"),
    s96("数据库返回为空"),
    s97("没有找到和当前CRM企业绑定的企业微信企业"),
    s98("请安装代开发应用"),
    s99("缺少参数deleteBindType"),
    s100("缺少参数corpIds"),
    s101("缺少参数appId"),
    s102("缺少参数fsEa"),
    s103("部门"),
    s104("员工"),
    s105("企微人员和部门绑定表"),
    s106("纷享EA"),
    s107("企微企业ID"),
    s108("纷享部门ID"),
    s109("纷享部门名称"),
    s110("企微部门ID"),
    s111("企微部门名称"),
    s112("示例：请删除该行"),
    s113("纷享员工ID"),
    s114("纷享员工名称"),
    s115("企微员工ID"),
    s116("企微员工名称"),
    s117("找不到应用绑定关系"),
    s118("应用安装异常，请联系CRM管理员"),
    s119("用code换企业微信用户信息失败"),
    s120("code可能已过期，请重新打开页面"),
    s121("免登失败"),
    s122("请关闭页面，重新登陆进去纷享crm"),
    s123("人员点击应用登陆，该企业还没有开通完成，请及时关注！"),
    s124("人员从企微登陆纷享失败告警"),
    s125("人员点击应用登陆，该企业还没有绑定关系。企微企业outEa=%s，企微人员OutUserId=%s，traceId=%s请及时关注！"),
    s126("通过企业微信数据(corpId=%s，userId=%s)，找不到绑定的纷享企业"),
    s127("请检查该企业是否已经绑定纷享crm，如果纷享应用安装时间超过一个小时，纷享企业还没有开通成功，请在企微卸载纷享应用，重新安装"),
    s128("登陆失败"),
    s129("企微和crm的绑定状态为已停用"),
    s130("人员点击应用登陆，该人员账号未创建成功，请及时关注！"),
    s131("该员工账号未创建，如果超过一个小时还未创建成功，请点击下面按钮重新创建员工账号"),
    s132("请检查该员工企微账号是否已绑定纷享crm员工账号"),
    s133("获取纷享crm人员信息失败"),
    s134("纷享crm人员已停用"),
    s135("ticket获取失败，请检查账号是否已绑定或者是否已被停用"),
    s136("请检查该企业微信企业是否已绑定纷享企业"),
    s137("请检查该企业是否已经绑定纷享crm"),
    s138("纷享销客CRM应用未安装，请先安装纷享销客CRM应用"),
    s139("纷享销客CRM应用未安装"),
    s140("请安装纷享销客CRM应用后，再用纷享销客CRM应用管理员身份扫码"),
    s141("当前扫码的人，不是纷享销客CRM应用管理员"),
    s142("请用企微纷享销客CRM应用管理员身份扫码"),
    s143("人员点击应用登陆，该企业还没有开通完成。企微企业outEa=%s，企微人员OutUserId=%s，纷享企业ea=%s，traceId=%s。请及时关注！"),
    s144("1、请关闭页面，重新登陆进去纷享crm。2、如果纷享应用安装时间超过一个小时，纷享企业还没有开通成功，请在企微卸载纷享应用，重新安装"),
    s145("人员点击应用登陆，该人员账号未创建成功。企微企业ea=%s，企微人员id=%s，纷享企业ea=%s，traceId=%s。请及时关注！"),
    s146("1、请检查该员工在crm是否已停用。2、确认该员工是否在crm和代开发应用可见范围内。3、重新触发下crm可见范围。4、如果是反绑定的企业，重新绑定下该员工的账号"),
    s147("通过企业微信数据(corpId=%s，userId=%s)，找不到绑定的员工账号"),
    s148("通过企业微信数据(corpId=%s，userId=%s)，创建纷享员工账号失败"),
    s149("通过企业微信数据(corpId=%s，userId=%s)，该企业纷享账号初始化中"),
    s150("通过企业微信数据(corpId=%s，userId=%s)，该员工账号绑定关系已停用"),
    s151("请重新登陆或者检查该账号(%s.%s)在crm是否已创建"),
    s152("请检查该账号(%s.%s)是否已被停用"),
    s153("通过企业微信数据(corpId=%s，userId=%s)，找不到绑定的企业账号"),
    s154("通过企业微信ID=%s找不到绑定的纷享企业"),
    s155("人员点击应用登陆，该企业还没有绑定关系，请及时关注！"),
    s156("1、该员工是否在企微纷享应用可见范围。2、该企业无企微接口权限，请联系纷享客服处理"),
    s157("该企业纷享crm员工账号已达上限，请联系纷享客服处理"),
    s158("ticket换取纷享员工身份失败，请及时关注！"),
    s159("ticket换取纷享员工身份失败ticket=%s，info=%s请及时关注！"),
    s160("页面出错了"),
    s161("请联系贵公司CRM管理员或IT管理员根据以下错误信息进行排查"),
    s162("重新同步此账号"),
    s163("创建成功，请重新打开页面"),
    s164("创建失败，请稍后重新点击同步"),
    s165("同步中..."),
    s166("纷享销客"),
    s167("错误信息："),
    s168("排查建议："),
    s169("纷享EA&企微企业ID&纷享员工ID&企微员工ID 字段不能为空"),
    s170("纷享员工不存在，纷享员工ID错误，纷享员工ID="),
    s171("纷享EA&企微企业ID 字段不是当前企微连接器绑定的"),
    s172("企微用户不在可见范围或者企微用户ID错误，企微用户ID="),
    s173("已经存在纷享员工映射关系，纷享员工ID="),
    s174("已经存在企微员工映射关系，企微员工ID="),
    s175("员工导入结果："),
    s176("解析成功【%s】条，新增数据【%s】条，更新数据【%s】条"),
    s177("处理失败【%s】条："),
    s178("  行号【%s】：%s"),
    s179("解析异常【%s】条："),
    s180("纷享EA&企微企业ID&纷享部门ID&企微部门ID 字段不能为空"),
    s181("纷享部门ID不存在"),
    s182("企微部门不在可见范围或企微部门ID错误"),
    s183("企微部门已经和纷享企业部门绑定，已绑定的纷享企业部门="),
    s184("企微部门已经和别的纷享企业的部门存在绑定关系，存在绑定关系的纷享企业EA="),
    s185("纷享部门已经和企微部门绑定，已绑定的企微部门="),
    s186("纷享部门已经和别的企微企业的部门存在绑定关系，存在绑定关系的企微企业ID="),
    s187("部门导入结果："),
    s188("导入结果留意企信通知"),
    s189("企微连接器人员导入结果"),
    s190("正常"),
    s191("停用"),
    s192("用户不在可见范围"),
    s193("人员绑定"),
    s194("企微人员绑定表"),
    s195("数据导出完成，您可以点击下载："),
    s196("企微人员绑定数据导出结果："),
    s197("用户部门不在可见范围"),
    s198("人员导入功能，仅支持三方CRM应用场景，不支持纯代开发应用场景"),
    s199("企微人员身份验证不通过，不能继续访问CRM。验证结果来自关联的APL函数。"),
    s200("纷享员工状态"),
    s201("绑定状态"),
    s202("绑定时间"),
    ;
    final String i18nKey;
    final String i18nValue;

    I18NStringEnum(String i18nValue) {
        i18nKey = "erpdss_outer_oa_connector.qywx.global." + name();
        this.i18nValue = i18nValue;
    }

    //前端请求没走异步，和是统一的异步Controller的异步一定可以用，其他的自己斟酌
    public String get() {
        String locale = TraceContext.get().getLocale();
        if (locale == null) {
            locale = "zh-CN";
        }
        try {
            return get(locale);
        } catch (Exception ignore) {
            return i18nValue;
        }
    }

    public String get(String locale) {
        return I18nClient.getInstance().get(this.i18nKey, 0, locale, this.i18nValue);
    }

    public static void main(String[] args) {
        String filePath = "d:/i18n/I18NStringEnum.csv";
        StringBuilder sb = new StringBuilder();
        for(I18NStringEnum codeEnum : I18NStringEnum.values()) {
            if(StringUtils.isEmpty(codeEnum.getI18nValue())) continue;
            sb.append(codeEnum.getI18nKey()+","+codeEnum.getI18nValue()+"\n");
        }
        try {
            FileUtils.writeStringToFile(new File(filePath),sb.toString(),"utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("success");
    }
}
