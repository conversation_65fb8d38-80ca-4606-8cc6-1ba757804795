package com.facishare.open.qywx.eventhandler.config;

import com.alibaba.fastjson.JSONArray;
import com.facishare.open.qywx.eventhandler.arg.fsAuthInfo;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by l<PERSON><PERSON> on 2018/11/01
 */
@Component
@Data
public class ConfigCenter {

    public static String USE_TOOLS_ACCOUNT = "{\"84883\":[1021,1000]}";;//"{\"84883\":[\"1021\",\"1000\"]}";
    //需要鉴权的接口列表
    public static List<String> needAuthInterfaceList;
    //合法的用户角色
    public static List<String> validRoleCodeList;
    //密钥
    public static String BASE64_SECRET = "";

    public static String verifyFsAuth = "{\"13834df3cf621aecb29dfd7b33160a64\":{\"fsKey\":\"3d96d60ca4f759af45b073c47524ebf8\",\"fsSecret\":\"7884c76b66daeff4b24e71e8ed301fb2\"},\"d6790f8b612c39e11c38f7ee763e7396\":{\"fsKey\":\"5d16a9583e459e5dc8d308452aef19d2\",\"fsSecret\":\"b0cb88aabd352bf05c5c775fafe3f74f\"}}";
    public static List<String> verifyFsAuthUrlList;

    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";

    public static String SERVICE_PROVIDER;

    /**
     * 接收告警人
     */
    public static Set<String> NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 企业开通接收告警人
     */
    public static Set<String> ENTERPRISE_OPEN_NOTIFICATION_MEMBERS = Sets.newHashSet();

    /**
     * 接收告警企业
     */
    public static String NOTIFICATION_EA = "84883";

    public static String ERROR_PAGE_TOKEN = "";

    public static String CLOUD_DOMAIN_EA = "{\"84883\":\"https://www.ceshi112.com\",\"74860\":\"https://www.ceshi112.com\"}";

    public static String qywx_scan_code_auth_success_redirect_url = "https://a9.fspage.com/FSR/weex/erpdss/oa-pages.html";
    public static String qywx_scan_code_auth_redirect_url = "";
    public static String crmAppId = "";

    //企微免登录鉴权配置，在免登录过程中，调用三方的身份验证APL函数
    public static String ssoAuthConfig = "";
    public static String paasFunctionUrl = "";

    static {
        ConfigFactory.getInstance().getConfig("fs-open-qywx-app-config", config -> {
            USE_TOOLS_ACCOUNT = config.get("USE_TOOLS_ACCOUNT", USE_TOOLS_ACCOUNT);

            needAuthInterfaceList = JSONArray.parseArray(config.get("need_auth_interface_list","[]"),String.class);
            validRoleCodeList = JSONArray.parseArray(config.get("valid_role_code_list","[]"),String.class);
            BASE64_SECRET = config.get("BASE64_SECRET", BASE64_SECRET);
            verifyFsAuth = config.get("VERIFY_FS_AUTH", verifyFsAuth);
            verifyFsAuthUrlList = JSONArray.parseArray(config.get("VERIFY_FS_AUTH_URL_LIST","[]"),String.class);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            SERVICE_PROVIDER = config.get("SERVICE_PROVIDER", SERVICE_PROVIDER);
            NOTIFICATION_EA = config.get("NOTIFICATION_EA", NOTIFICATION_EA);
            NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("NOTIFICATION_MEMBERS", "")));
            ENTERPRISE_OPEN_NOTIFICATION_MEMBERS =  ImmutableSet.copyOf(Splitter.on(",").split(config.get("ENTERPRISE_OPEN_NOTIFICATION_MEMBERS", "")));
            ERROR_PAGE_TOKEN = config.get("ERROR_PAGE_TOKEN", ERROR_PAGE_TOKEN);
            CLOUD_DOMAIN_EA = config.get("CLOUD_DOMAIN_EA", CLOUD_DOMAIN_EA);
            qywx_scan_code_auth_success_redirect_url = config.get("qywx_scan_code_auth_success_redirect_url", qywx_scan_code_auth_success_redirect_url);
            qywx_scan_code_auth_redirect_url = config.get("qywx_scan_code_auth_redirect_url",qywx_scan_code_auth_redirect_url);
            crmAppId = config.get("crmAppId", crmAppId);
            ssoAuthConfig = config.get("ssoAuthConfig", ssoAuthConfig);
            paasFunctionUrl = config.get("paas.function.url", paasFunctionUrl);
        });
    }

    public static void main(String[] args) {
        Map<String, fsAuthInfo> verifyFsAuthMap = new Gson().fromJson(verifyFsAuth, new TypeToken<Map<String, fsAuthInfo>>() {
        });
        System.out.println(verifyFsAuthMap);
    }
}
