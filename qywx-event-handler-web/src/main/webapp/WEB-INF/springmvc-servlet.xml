<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc-3.1.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">
    <!--annotation configuration -->
    <import resource="classpath:*/spring-cms.xml" />
    <import resource="classpath:*/spring-common.xml" />
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <context:annotation-config />
    <context:component-scan base-package="com.facishare.open.qywx.eventhandler" />
    <mvc:default-servlet-handler/>
    <mvc:annotation-driven>
        <mvc:message-converters>
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
                <constructor-arg ref="utf8Charset" />
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>
    <bean id="utf8Charset" class="java.nio.charset.Charset" factory-method="forName">
        <constructor-arg value="UTF-8" />
    </bean>

    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.facishare.open.qywx.eventhandler.aop.UserInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>
    <mvc:default-servlet-handler />
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/" />               <!-- 配置前缀 -->
        <property name="suffix" value="" />                    <!-- 配置后缀 -->
    </bean>
</beans>


