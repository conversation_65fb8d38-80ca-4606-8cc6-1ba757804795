package com.facishare.open.qywx.accountsync.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;

import java.lang.reflect.Type;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/25
 */
public class GsonUtil {
    private static final Gson GSON = new GsonBuilder().disableHtmlEscaping().create();


    public GsonUtil() {
    }


    public static Gson getGson() {
        return GSON;
    }


    public static String toJson(Object object) {
        if (object == null) {
            return "";
        }
        if (object instanceof String) {
            return (String) object;
        }
        return GSON.toJson(object);
    }

    public static <T> T fromJson(String json, Type type) {
        return GSON.fromJson(json, type);
    }

    public static <T> T fromJson(JsonElement json, Type type) {
        return GSON.fromJson(json, type);
    }

    public static Boolean equals(Object obj1, Object obj2) {
        return toJson(obj1).equals(toJson(obj2));
    }

    public static void main(String[] args) {
        String str = null;
        System.out.println(toJson(str));
        System.out.println(GSON.toJson(str));
        System.out.println(str);
        String str2 = fromJson(str, String.class);
        System.out.println(str2);

    }
}
