package com.facishare.open.qywx.accountsync.utils.xml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * 企业微信推送数据事件
 * <AUTHOR>
 * @date 2021/09/14
 */

@XStreamAlias("xml")
@Data
public class QYWeiXinDataEventBaseXml {
    @XStreamAlias("ToUserName")
    public String ToUserName;//企业微信CorpID

    @XStreamAlias("FromUserName")
    public String FromUserName;//成员UserID

    @XStreamAlias("CreateTime")
    public String CreateTime;//消息创建时间（整型）

    @XStreamAlias("MsgType")
    public String MsgType;//消息类型，此时固定为：event

    @XStreamAlias("Event")
    public String Event;//事件类型，subscribe(关注)、unsubscribe(取消关注)
}
