package com.facishare.open.qywx.save.service;


import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.save.result.Result;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 15:46
 * @Version 1.0
 */

public interface MessageGeneratingService {

     Result<Integer> saveSetting(GenerateSettingVo generateSettingVo);

     Result<Integer> saveSecretSetting(GenerateSettingVo generateSettingVo);

     Result<GenerateSettingVo> querySettingByAuto(String ea, Integer version, String outEa);

     Result<GenerateSettingVo> querySetting(String ea, Integer version, String outEa);

     Result<List<String>> queryAllSetting();

     Result<List<GenerateSettingVo>> queryByEaSetting(String ea);

     Result<GenerateSettingVo> querySecretSetting(String ea);

     Result<Void> sendMessage(String ea);

     Result<Integer> updateCorpRepSecret(GenerateSettingVo generateSettingVo);

     Result<Void> updateCorpSetting();

     Result<MessageStorageArg> getMessageStorageLocation(String fsEa, Integer version, String serviceKey, String outEa);

}
