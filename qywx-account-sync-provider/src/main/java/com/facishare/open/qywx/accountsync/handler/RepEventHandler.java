package com.facishare.open.qywx.accountsync.handler;

import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.accountsync.utils.xml.EventXml;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业微信代开发应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class RepEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;

    @Override
    public void handle(String plainMsg) {
        super.handle(plainMsg);

        //String plainMsg = null;
        try {
//            plainMsg = decryptMsg(eventProto.getSignature(), eventProto.getTimestamp(),
//                    eventProto.getNonce(), eventProto.getData(),eventProto.getAppId());
            log.info("RepEventHandler.handle,plainMsg={}", plainMsg);

            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinMsgBaseXml.class);
            log.info("RepEventHandler.handle,baseMsgXml={}",baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("RepEventHandler.handle,infoType={}",infoType);

            if(StringUtils.isEmpty(infoType)) {
                EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
                infoType = eventXml.getEvent();
                log.info("RepEventHandler.handle,infoType2={}",infoType);
            }
            if(!supportedCmdEvent.contains(infoType)) {
                log.info("RepEventHandler.handle，not support event,infoType={}",infoType);
                return;
            }
            //去掉不支持的外部联系人事件
            List<String> externalContactChangeTypeList = Lists.newArrayList("add_external_contact","edit_external_contact","del_external_contact","del_follow_user");
            if(StringUtils.equalsIgnoreCase("change_external_contact",infoType)
                    && !externalContactChangeTypeList.contains(baseMsgXml.getChangeType())) {
                return;
            }

            String outEa = org.apache.commons.lang3.StringUtils.isEmpty(baseMsgXml.getAuthCorpId()) ? baseMsgXml.getToUserName() : baseMsgXml.getAuthCorpId();
            log.info("RepEventHandler.repMsgEvent,outEa={}", outEa);
            if(runInCurrentEnv(outEa,plainMsg)==false) return;

        } catch (Exception e) {
            log.error("RepEventHandler.handle,exception={}", e.getMessage(),e);
            return;
        }

        log.info("RepEventHandler.repMsgEvent,plainMsg2={}", plainMsg);
        String result = qyweixinGatewayInnerService.repMsgEvent2(plainMsg);

        log.info("RepEventHandler.repMsgEvent,result={}", result);
    }
}
