package com.facishare.open.qywx.accountsync.job;

import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@JobHander(value = "AutoIdToOpenidHandler")
@Component
@Slf4j
public class AutoIdToOpenidHandler extends IJobHandler {

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        try {
            log.info("scan AutoIdToOpenidHandler start the job!");
            qyweixinAccountSyncService.autoIdToOpenid();
            log.info("scan AutoIdToOpenidHandler end the job!");
            return new ReturnT(ReturnT.SUCCESS_CODE, "定时任务调用成功");
        } catch (Exception e) {
            log.error("定时任务调用异常调用异常，error:", e);
            throw e;
        }
    }
}