package com.facishare.open.qywx.accountsync.manager;

import com.alibaba.fastjson.JSONObject;
import com.fxiaoke.crmrestapi.arg.ActionEditArg;
import com.fxiaoke.crmrestapi.arg.BatchCreateResult;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.BatchObjectDataResult;
import com.fxiaoke.crmrestapi.result.FindNameByIdsResult;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/10/29 18:57
 * @desc
 */
@Slf4j
@Component
public class CrmObjManager  {
    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataActionService metadataActionService;
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;



    /**
     * 新增数据
     */
    public void createObjData(Integer tenantId, ObjectData objectData,String objectApiName){
        if(!crmObjectSupportManager.createDefineObject(tenantId,objectApiName)){
            log.error("create wechat object fail:{}");
            return;
        }
        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        Result<ObjectDataCreateResult> result = objectDataService.create(headerObj,objectApiName,
                false,false,false,objectData);
        log.info("create data message result:{}", JSONObject.toJSONString(result));
    }

    /**
     * 批量新增
     */
    public void batchCreateData(Integer tenantId, List<ObjectData> objectDataList, String objectApiName){
        if(!crmObjectSupportManager.createDefineObject(tenantId,objectApiName)){
            log.error("create wechat object fail:{}");
            return;
        }
        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        Result<BatchCreateResult> batchCreateResultResult = objectDataService.batchCreate(headerObj, objectApiName, objectDataList);

        log.info("create data message result:{}", JSONObject.toJSONString(batchCreateResultResult));
    }
    /**
     * 批量更新
     */
    public void batchUpdateObject(Integer tenantId, List<ObjectData> objectDataList, String objectApiName){
        if(!crmObjectSupportManager.createDefineObject(tenantId,objectApiName)){
            log.error("create wechat object fail:{}");
            return;
        }
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        Result<BatchObjectDataResult> batchObjectDataResultResult = objectDataService.batchIncrementUpdate(headerObj, objectApiName, objectDataList);
        log.info("create data message result:{}", JSONObject.toJSONString(batchObjectDataResultResult));

    }

    /**
     * 批量查询
     */
    public Map<String,String> queryByActiveCodes(Integer tenantId, List<String> activeCodes,String objectApiName){
        if(!crmObjectSupportManager.createDefineObject(tenantId,objectApiName)){
            log.error("create wechat object fail:{}");
            return null;
        }
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        SearchTemplateQuery searchTemplateQuery=new SearchTemplateQuery();
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.addFilter("name",activeCodes,"IN");
        Result<QueryBySearchTemplateResult> queryBySearchTemplateResultResult = objectDataService.queryBySearchTemplate(headerObj, objectApiName, searchTemplateQuery);
        Map<String,String> nameIds=Maps.newHashMap();
        if(queryBySearchTemplateResultResult.isSuccess()){
            for (ObjectData datum : queryBySearchTemplateResultResult.getData().getQueryResult().getData()) {
                nameIds.put(datum.getName(),datum.getId());
            }
        }
//        Result<FindNameByIdsResult> nameByIds = objectDataService.findNameByIds(headerObj, objectApiName, activeCodes);
//        if(nameByIds.isSuccess()){
//            Map<String, String> objectIdsMap = nameByIds.getData().getNameList().stream().collect(Collectors.toMap(FindNameByIdsResult.IdName::getName, FindNameByIdsResult.IdName::getId,(v1,v2) -> v1));
//            return objectIdsMap;
//        }
        return nameIds;
    }

    public Map<String,String> queryWeChatEmployeeIds(Integer tenantId, List<String> qywxCipherCodes,String objectApiName){
        HeaderObj headerObj = HeaderObj.newInstance(tenantId, CrmConstants.SYSTEM_USER);
        SearchTemplateQuery searchTemplateQuery=new SearchTemplateQuery();
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.addFilter("cipher_user_id",qywxCipherCodes,"IN");
        Result<QueryBySearchTemplateResult> queryBySearchTemplateResultResult = objectDataService.queryBySearchTemplate(headerObj, objectApiName, searchTemplateQuery);
        Map<String,String> nameIds=Maps.newHashMap();
        if(queryBySearchTemplateResultResult.isSuccess()){
            for (ObjectData datum : queryBySearchTemplateResultResult.getData().getQueryResult().getData()) {
                nameIds.put(datum.get("cipher_user_id").toString(),datum.getId());
            }
        }
//        Result<FindNameByIdsResult> nameByIds = objectDataService.findNameByIds(headerObj, objectApiName, activeCodes);
//        if(nameByIds.isSuccess()){
//            Map<String, String> objectIdsMap = nameByIds.getData().getNameList().stream().collect(Collectors.toMap(FindNameByIdsResult.IdName::getName, FindNameByIdsResult.IdName::getId,(v1,v2) -> v1));
//            return objectIdsMap;
//        }
        return nameIds;
    }

//    public Map<String,String> queryByOrderId(String tenantId, String orderId,String objectApiName){
//        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
//        SearchTemplateQuery searchTemplateQuery=new SearchTemplateQuery();
//
//        Result<FindNameByIdsResult> nameByIds = objectDataService.queryBySearchTemplate(headerObj, objectApiName, activeCodes);
//        if(nameByIds.isSuccess()){
//            Map<String, String> objectIdsMap = nameByIds.getData().getNameList().stream().collect(Collectors.toMap(FindNameByIdsResult.IdName::getName, FindNameByIdsResult.IdName::getId,(v1,v2) -> v1));
//            return objectIdsMap;
//        }
//        return Maps.newHashMap();
//    }


    /**
     * 新增数据
     */
    public void updateObjData(Integer tenantId,String objectId, ObjectData objectData,String objectApiName){
        if(!crmObjectSupportManager.createDefineObject(tenantId,objectApiName)){
            log.error("create wechat object fail:{}");
            return ;
        }
        HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId), CrmConstants.SYSTEM_USER);
        ActionEditArg actionEditArg=new ActionEditArg();
        actionEditArg.setObjectData(objectData);
        objectDataService.updateObjectData(headerObj,objectApiName,objectId,true,objectData);
    }

}
