package com.facishare.open.qywx.accountsync.manager;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountinner.model.OutEventDateChangeProto;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinGetPermenantCodeRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpInfoBo;
import com.facishare.open.qywx.accountsync.mq.MQSender;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 跨云处理manager
 */
@Component
@Slf4j
public class EventCloudProxyManager {
    @Resource
    private MQSender mqSender;
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    public void eventCloudProxy(ChannelEnum channelEnum, String appId, String appType, String outEa, String eventType, String plainText, String doMain) {
        OutEventDateChangeProto proto = new OutEventDateChangeProto();
        proto.setAppId(appId);
        proto.setAppType(appType);
        proto.setEventType(eventType);
        proto.setOutEa(outEa);
        proto.setContent(plainText);
        proto.setDomain(doMain);
        mqSender.sendOutEventDataChangeMQ(channelEnum.name(), proto, "0");
    }

    public void handleEvent(OutEventDateChangeProto proto) {
        log.info("EventCloudProxyManager.handleEvent,proto={}", proto);
        //刷库
        if(StringUtils.isNotEmpty(proto.getAppType()) && proto.getAppType().equals("dataPush")) {
            dataPush(proto);
            return;
        }
        //suiteTicket
        if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("suite_ticket")) {
            qyWeixinManager.saveQyweixinCloudTicketToken(proto.getAppId(), proto.getContent());
            return;
        }
        //第三方应用事件特殊处理
        //比如授权事件，只需要纷享云解密一次就行，直接投递需要入库的数据
        if(StringUtils.isNotEmpty(proto.getAppType()) && proto.getAppType().equals(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD)) {
            dealCrmEvent(proto);
        }
        //代开发应用事件
        if(StringUtils.isNotEmpty(proto.getAppType()) && proto.getAppType().equals(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP)) {
            dealRepEvent(proto);
        }
    }

    private void dealCrmEvent(OutEventDateChangeProto proto) {
        //create_auth
        if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("create_auth")) {
            corpManager.saveCorpInfoTask(new Gson().fromJson(proto.getContent(), QyweixinGetPermenantCodeRsp.class), proto.getAppId());
        } else {
            //如果是非紧急事件，发送MQ，等待后面消费
            //继续这个逻辑，不要进行业务处理
            mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD, proto.getContent());
        }
    }

    private void dataPush(OutEventDateChangeProto proto) {
        if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("qyweixinCorpBindDao")) {
            if(proto.getDomain().equals(ConfigCenter.crm_domain)) {
                corpManager.saveOrUpdateCorpBindData(new Gson().fromJson(proto.getContent(), new TypeToken<List<QyweixinCorpBindBo>>(){}.getType()));
            }
          return;
        } else if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("accountEnterpriseBindDao")) {
            if(proto.getDomain().equals(ConfigCenter.crm_domain)) {
                List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = new Gson().fromJson(proto.getContent(), new TypeToken<List<QyweixinAccountEnterpriseMapping>>(){}.getType());
                for(QyweixinAccountEnterpriseMapping enterpriseMapping : enterpriseMappingList) {
                    enterpriseMapping.setDomain(ConfigCenter.crm_domain);
                    com.facishare.open.qywx.accountbind.result.Result<Boolean> ret = qyweixinAccountBindService.bindAccountEnterpriseMapping(enterpriseMapping);
                    log.info("EventCloudProxyManager.dataPush,ret={}", ret);
                }
            }
            return;
        } else if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("qyweixinAccountEmployeeBindDao")) {
            if(proto.getDomain().equals(ConfigCenter.crm_domain)) {
                List<QyweixinAccountEmployeeMapping> employeeMappingList = new Gson().fromJson(proto.getContent(), new TypeToken<List<QyweixinAccountEmployeeMapping>>(){}.getType());
                com.facishare.open.qywx.accountbind.result.Result<Boolean> ret = qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
                log.info("EventCloudProxyManager.dataPush,ret={}", ret);
            }
            return;
        } else if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("QyweixinCorpInfoBo")) {
            if(proto.getDomain().equals(ConfigCenter.crm_domain)) {
                corpManager.saveOrUpdateCorpInfoData(new Gson().fromJson(proto.getContent(), new TypeToken<List<QyweixinCorpInfoBo>>(){}.getType()));
            }
            return;
        }
        log.info("EventCloudProxyManager.dataPush,proto={}", proto);
    }

    private void dealRepEvent(OutEventDateChangeProto proto) {
        //create_auth
        if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("create_auth")) {
            corpManager.saveRepInfoTask(new Gson().fromJson(proto.getContent(), QyweixinGetPermenantCodeRsp.class), proto.getAppId());
        } else if(StringUtils.isNotEmpty(proto.getEventType()) && proto.getEventType().equals("reset_permanent_code")) {
            corpManager.saveRepCorpInfoTask(new Gson().fromJson(proto.getContent(), QyweixinGetPermenantCodeRsp.class), proto.getAppId());
        } else {
            //如果是非紧急事件，发送MQ，等待后面消费
            //继续这个逻辑，不要进行业务处理
            mqSender.sendEnterpriseWeChatMQ(EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP, proto.getContent());
        }
    }
}
