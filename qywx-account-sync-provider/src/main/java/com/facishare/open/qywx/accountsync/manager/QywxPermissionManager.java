package com.facishare.open.qywx.accountsync.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oauth.model.enums.AccessTypeEnum;
import com.facishare.open.oauth.model.enums.EaAuthStatusEnum;
import com.facishare.open.oauth.result.GetFsUserIdsResult;
import com.facishare.open.oauth.service.AuthService;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.accountsync.crm.CrmUrlUtils;
import com.facishare.open.qywx.accountsync.limiter.CrmRateLimiter;
import com.facishare.open.qywx.accountsync.model.GetFsUserIdsByRestResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.HttpResponseMessage;
import com.facishare.open.qywx.accountsync.utils.OkHttp3MonitorUtils;
import com.facishare.organization.adapter.api.model.biz.RunStatus;
import com.facishare.organization.adapter.api.model.biz.department.Department;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetAllDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.arg.GetDepartmentArg;
import com.facishare.organization.adapter.api.model.biz.department.result.GetDepartmentResult;
import com.facishare.organization.adapter.api.model.biz.employee.arg.BatchGetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.BatchGetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.DepartmentService;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.*;
import com.facishare.organization.api.model.employee.result.*;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseRunStatusArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseRunStatusResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ActionChangeOwnerArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionChangeOwnerResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  处理企微系统处理事件
 */
@Slf4j
@Component
public class QywxPermissionManager {

    @Resource
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CrmObjManager crmObjManager;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private EIEAConverter eieaConverter;

    public void handlerOrderPaySuccess(String orderId){
        try {
            QywxPermissionOrderDetail permitOrder = qyWeixinManager.getPermitOrder(orderId);
            if(permitOrder.isSuccess()&&permitOrder.getOrder().getOrder_status()!=1){
                //1：已支付
                log.info("handler notpaysuccess:{}", JSONObject.toJSONString(permitOrder));
                return;
            }
            String corpId=permitOrder.getOrder().getCorpid();
            List<QywxPermissionOrderAccountDetail.OrderAccountList> permitOrderAccount = qyWeixinManager.getPermitOrderAccount(orderId);
            List<ObjectData> createCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
            List<ObjectData> updateCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
            //根据账号查询
            List<List<QywxPermissionOrderAccountDetail.OrderAccountList>> partition = Lists.partition(permitOrderAccount, 100);//企业微信最多1000
            for (List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists : partition) {
                //根据corpId查询绑定fsea
                List<QyweixinAccountEnterpriseMapping> qyweixinAccountEnterpriseMappings = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);

                for (QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping : qyweixinAccountEnterpriseMappings) {
                    String  fsEa = qyweixinAccountEnterpriseMapping.getFsEa();
                    int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
                    if(!ConfigCenter.USE_QYWX_PERMISSION_EALISTS.contains(fsEa)){
                        log.info("handler message tenantid notexist:{}",fsEa);
                        return;
                    }
                    List<String> activeCodes = accountLists.stream().map(QywxPermissionOrderAccountDetail.OrderAccountList::getActive_code).collect(Collectors.toList());
                    QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode(permitOrder.getOrder().getCorpid(), activeCodes);
                    log.info("permit order :{}"+JSONObject.toJSONString(activeInfoByCode));
                    //批量创建
                    //批量获取activeInfo的userid.查询关联企业微信员工id
                    Map<String, String> crmobjDataMap = crmObjManager.queryByActiveCodes(tenantId, activeCodes, Constant.WechatInterfaceLicenseObj);
                    List<String> wechatEmpData = activeInfoByCode.getActive_info_list().stream().map(QywxActiveCodeDetail.ActiveCodeInfo::getUserid).collect(Collectors.toList());
                    Map<String, String> weChatEmployeeIds = crmObjManager.queryWeChatEmployeeIds(tenantId, wechatEmpData, Constant.WechatEmployeeObj);
                    for (QywxActiveCodeDetail.ActiveCodeInfo activeCodeInfo : activeInfoByCode.getActive_info_list()) {
                        ObjectData crmWechatInterfaceLicenseObj=new ObjectData();
                        crmWechatInterfaceLicenseObj.put("account_status",activeCodeInfo.getStatus());
                        crmWechatInterfaceLicenseObj.put("name",activeCodeInfo.getActive_code());
                        crmWechatInterfaceLicenseObj.put("account_type",activeCodeInfo.getType());
                        crmWechatInterfaceLicenseObj.put("bind_wechat_userid",activeCodeInfo.getUserid());
                        if(activeCodeInfo.getActive_time()!=null){
                            crmWechatInterfaceLicenseObj.put("active_time",activeCodeInfo.getActive_time()*1000);
                        }
                        if(activeCodeInfo.getExpire_time()!=null){
                            crmWechatInterfaceLicenseObj.put("expire_time",activeCodeInfo.getExpire_time()*1000);
                        }
                        if(activeCodeInfo.getUserid()!=null){
                            crmWechatInterfaceLicenseObj.put("wechat_employee_id",weChatEmployeeIds.get(activeCodeInfo.getUserid()));
                        }
                        if(activeCodeInfo.getMerge_info()!=null){
                            crmWechatInterfaceLicenseObj.put("to_active_code",activeCodeInfo.getMerge_info().getTo_active_code());
                            crmWechatInterfaceLicenseObj.put("from_active_code",activeCodeInfo.getMerge_info().getFrom_active_code());
                        }
                        crmWechatInterfaceLicenseObj.put("pay_time",permitOrder.getOrder().getPay_time()*1000);
                        crmWechatInterfaceLicenseObj.put("order_id",orderId);
                        crmWechatInterfaceLicenseObj.put("account_duration",permitOrder.calculateDuration());
                        crmWechatInterfaceLicenseObj.put("active_expire_time",permitOrder.calculateActiveExpireTime());
                        crmWechatInterfaceLicenseObj.put("owner",Lists.newArrayList("-10000"));
                        if(crmobjDataMap.get(activeCodeInfo.getActive_code())!=null){
                            //更新
                            String dataId = crmobjDataMap.get(activeCodeInfo.getActive_code());
                            crmWechatInterfaceLicenseObj.put("_id",dataId);
                            crmWechatInterfaceLicenseObj.remove("owner");
                            updateCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
                        }else{
                            //新增
                            createCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(createCrmWechatInterfaceLicenseObjs)){
                        crmObjManager.batchCreateData(tenantId,createCrmWechatInterfaceLicenseObjs, Constant.WechatInterfaceLicenseObj);
                    }else {
                        crmObjManager.batchUpdateObject(tenantId,updateCrmWechatInterfaceLicenseObjs,Constant.WechatInterfaceLicenseObj);
                    }
                }

            }
        } catch (Exception e) {
            log.error("system handler paysuccess fail :{}",e.getMessage());
        }

    }

    private void commonHandlerEvent(List<String> activeCodes, Integer tenantId, String corpId, List<QywxActiveCodeDetail.ActiveCodeInfo> activeCodeInfos){
        if(ObjectUtils.isEmpty(activeCodes)&&ObjectUtils.isEmpty(activeCodeInfos)){
            return;
        }
        List<ObjectData> createCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
        List<ObjectData> updateCrmWechatInterfaceLicenseObjs=Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(activeCodes)){
            QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode(corpId, activeCodes);
            activeCodeInfos=activeInfoByCode.getActive_info_list();
        }
        if(CollectionUtils.isNotEmpty(activeCodeInfos)&&CollectionUtils.isEmpty(activeCodes)){
            activeCodes= activeCodeInfos.stream().map(QywxActiveCodeDetail.ActiveCodeInfo::getActive_code).collect(Collectors.toList());
        }
        Map<String, String> crmobjDataMap = crmObjManager.queryByActiveCodes(tenantId, activeCodes, Constant.WechatInterfaceLicenseObj);
        List<String> wechatEmpData = activeCodeInfos.stream().map(QywxActiveCodeDetail.ActiveCodeInfo::getUserid).collect(Collectors.toList());
        Map<String, String> weChatEmployeeIds = crmObjManager.queryWeChatEmployeeIds(tenantId, wechatEmpData, Constant.WechatEmployeeObj);
        for (QywxActiveCodeDetail.ActiveCodeInfo activeCodeInfo : activeCodeInfos) {
            ObjectData crmWechatInterfaceLicenseObj=new ObjectData();
            crmWechatInterfaceLicenseObj.put("account_status",activeCodeInfo.getStatus());
            crmWechatInterfaceLicenseObj.put("name",activeCodeInfo.getActive_code());
            crmWechatInterfaceLicenseObj.put("account_type",activeCodeInfo.getType());
            crmWechatInterfaceLicenseObj.put("bind_wechat_userid",activeCodeInfo.getUserid());
            if(activeCodeInfo.getActive_time()!=null){
                crmWechatInterfaceLicenseObj.put("active_time",activeCodeInfo.getActive_time()*1000);
            }
            if(activeCodeInfo.getExpire_time()!=null){
                crmWechatInterfaceLicenseObj.put("expire_time",activeCodeInfo.getExpire_time()*1000);
            }
            if(activeCodeInfo.getUserid()!=null){
                String objectId = weChatEmployeeIds.get(activeCodeInfo.getUserid());
                crmWechatInterfaceLicenseObj.put("wechat_employee_id",objectId);
            }

            if(activeCodeInfo.getMerge_info()!=null){
                crmWechatInterfaceLicenseObj.put("to_active_code",activeCodeInfo.getMerge_info().getTo_active_code());
                crmWechatInterfaceLicenseObj.put("from_active_code",activeCodeInfo.getMerge_info().getFrom_active_code());
            }

            crmWechatInterfaceLicenseObj.put("owner",Lists.newArrayList("-10000"));
            if(crmobjDataMap.get(activeCodeInfo.getActive_code())!=null){
                //更新
                String dataId = crmobjDataMap.get(activeCodeInfo.getActive_code());
                crmWechatInterfaceLicenseObj.put("_id",dataId);
                crmWechatInterfaceLicenseObj.remove("owner");
                updateCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
            }else{
                //新增
                createCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
            }
        }
        if(CollectionUtils.isNotEmpty(createCrmWechatInterfaceLicenseObjs)){
            crmObjManager.batchCreateData(tenantId,createCrmWechatInterfaceLicenseObjs, Constant.WechatInterfaceLicenseObj);
        }else {
            crmObjManager.batchUpdateObject(tenantId,updateCrmWechatInterfaceLicenseObjs,Constant.WechatInterfaceLicenseObj);
        }
    }

    public void handlerAutoActive(List<String> activeCodes,String corpId){
        List<QyweixinAccountEnterpriseMapping> qyweixinAccountEnterpriseMappings = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        for (QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping : qyweixinAccountEnterpriseMappings) {
            String  fsEa = qyweixinAccountEnterpriseMapping.getFsEa();
            int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
            if(!ConfigCenter.USE_QYWX_PERMISSION_EALISTS.contains(fsEa)){
                log.info("handler message tenantid notexist:{}",fsEa);
                return;
            }
            //批量创建
            commonHandlerEvent(activeCodes,tenantId,corpId,null);
        }
    }
    //支持指定企业账号列表，存储激活信息
    public void handlerAccountListByCorpId(String corpId){
        List<QyweixinAccountEnterpriseMapping> qyweixinAccountEnterpriseMappings = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        for (QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping : qyweixinAccountEnterpriseMappings) {
            String  fsEa = qyweixinAccountEnterpriseMapping.getFsEa();
            int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
            //批量创建
            List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists = qyWeixinManager.getEnterpriseActiveAccount(corpId);
            Set<String> distinctAccounts = accountLists.stream().map(item -> item.getUserid()).collect(Collectors.toSet());
            List<QywxActiveCodeDetail.ActiveCodeInfo> details=Lists.newArrayList();
            List<String> activeCodesList = Lists.newArrayList();
            for (String distinctAccount : distinctAccounts) {
                QywxActiveCodeDetail userAccountList = qyWeixinManager.getActiveInfoByUserId(corpId, distinctAccount);
                activeCodesList.addAll(userAccountList.getActive_info_list().stream().map(item -> item.getActive_code()).collect(Collectors.toList()));
//                userAccountList.getActive_info_list().forEach(item ->item.setStatus(userAccountList.getActive_status()));
//                details.addAll(userAccountList.getActive_info_list());
            }
            List<List<String>> partition = Lists.partition(activeCodesList, 100);
            //保护底层
            for (List<String> activeCodes : partition) {
                commonHandlerEvent(activeCodes,tenantId,corpId,null);
            }

        }
    }

    //支持指定企业，查询时间内的订单
    public void handlerBetweenTime(String corpId,Long startTime,Long endTime){
        QywxPermissionOrderList orderList = qyWeixinManager.getPayOrderList(startTime, endTime, corpId);
        if(orderList.isSuccess()){
            for (QywxPermissionOrderList.OrderList list : orderList.getOrder_list()) {
                handlerOrderPaySuccess(list.getOrder_id());
            }
        }
    }






}
