package com.facishare.open.qywx.accountsync.manager;

import com.alibaba.fastjson.*;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.*;
import com.facishare.open.qywx.accountsync.dao.PlatformAppAdminInfoDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpInfoDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinOrderInfoDao;
import com.facishare.open.qywx.accountsync.limiter.CrmRateLimiter;
import com.facishare.open.qywx.accountsync.model.ChangeContactEvent;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.QyweixinSendMsgEvent;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.PlatformAppAdminInfoBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpInfoBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinOrderInfoBo;
import com.facishare.open.qywx.accountsync.mq.MQSender;
import com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.SecurityUtil;
import com.facishare.open.qywx.accountsync.utils.xml.ContactsXml;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinMemberEventXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.result.FindEmployeeDtoByFullNameResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 处理企业信息存储，更新，企业开通，通讯录变更消息发送
 * Created by liuwei on 2018/09/06
 */
@Slf4j
@Component
public class CorpManager {

    ExecutorService corpManagerThreadPool = Executors.newCachedThreadPool();

    @Resource(name = "qywxEventNotifyMQSender")
    private AutoConfRocketMQProducer qywxEventNotifyMQSender;

    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;

    @Autowired
    private QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private PlatformAppAdminInfoDao platformAppAdminInfoDao;

    @Autowired
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;

    @Autowired
    private OrderManager orderManager;

    @Autowired
    private MessageGeneratingService messageGeneratingService;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private ContactsService contactsService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @ReloadableProperty("contactAppId")
    private String contactAppId;

    @ReloadableProperty("repAppId")
    private String repAppId;

    @Autowired
    private MQSender mqSender;

    private HttpHelper httpHelper = new HttpHelper();

    public static final String TAG_ENTERPRISE = "qywx_enterprise_order";
    public static final String TAG_ORGANIZATION = "qywx_update_organization";
    public static final String TAG_MESSAGE = "qywx_send_msg";
    public static final String TAG_EXTERNAL_CONTACT = "tag_external_contact";
    public static final String TAG_DELETE_EXTERNAL_CONTACT = "tag_delete_external_contact";
    public static final String TAG_CREATE_USER = "tag_create_user";
    public static final String TAG_UPDATE_USER = "tag_update_user";
    public static final String TAG_DELETE_USER = "tag_delete_user";
    public static final String TAG_CREATE_EXTERNAL_CHAT = "tag_create_external_chat";
    public static final String TAG_UPDATE_EXTERNAL_CHAT = "tag_update_external_chat";
    public static final String TAG_DISMISS_EXTERNAL_CHAT = "tag_dismiss_external_chat";

    /**
     * 历史待办消息通知接口
     */
    @ReloadableProperty("historyToDoMessageUrl")
    private String historyToDoMessageUrl;

    /**
     * 审批流，业务流通知接口
     */
    @ReloadableProperty("flowTaskUrl")
    private String flowTaskUrl;

    @Autowired
    private EIEAConverter eieaConverter;
    /**
     * 各个应用和试用版本之间的关系
     */
    @ReloadableProperty("tryEditionMapping")
    private String tryEditionMapping;
    Map<String, String> tryEditionMap = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        tryEditionMap = JSON.parseObject(tryEditionMapping, new TypeReference<Map<String, String>>(){});
        log.info("init tryEditionMapping success. tryEditionMapping:{}, tryEditionMap:{}", tryEditionMapping, tryEditionMap);
    }

    /**
     * 1.authCode获取企业信息
     * 2.发送开通企业mq
     * 3.异步保存企业信息
     * @param corpAuthInfo
     * @param appId
     * @return
     */
    public Result<String> initCorpEvent(QyweixinGetPermenantCodeRsp corpAuthInfo, String appId, String authCode) {

        //发送开通企业通知消息
        QyweixinEnterpriseOrder qyweixinAddEnterprise = new QyweixinEnterpriseOrder();

        QyweixinAgentRsp qyweixinAgentRsp = null;
        if(corpAuthInfo.getAuth_info().getAgent()!=null && corpAuthInfo.getAuth_info().getAgent().size()>0) {
            qyweixinAgentRsp = corpAuthInfo.getAuth_info().getAgent().get(0);
            if(qyweixinAgentRsp.getAuth_mode()==1) { //成员授权
                com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAdminListRsp> qyweixinGetAdminListRspResult = qyWeixinManager.getAdminList(corpAuthInfo.getAuth_corp_info().getCorpid(),
                        appId,
                        qyweixinAgentRsp.getAgentid());

                log.info("CorpManager.initCorpEvent,qyweixinGetAdminListRsp={}",qyweixinGetAdminListRspResult);

                if(qyweixinGetAdminListRspResult.isSuccess()
                        && ObjectUtils.isNotEmpty(qyweixinGetAdminListRspResult.getData())
                        && qyweixinGetAdminListRspResult.getData().getAdmin()!=null
                        && qyweixinGetAdminListRspResult.getData().getAdmin().size()>0) {
                    QyweixinGetAdminListRsp.AdminModel adminModel = null;
                    QyweixinGetAdminListRsp qyweixinGetAdminListRsp = qyweixinGetAdminListRspResult.getData();
                    for(QyweixinGetAdminListRsp.AdminModel item : qyweixinGetAdminListRsp.getAdmin()) {
                        if(item.getAuth_type()==1) {
                            adminModel = item;
                            break;
                        }
                    }
                    log.info("CorpManager.initCorpEvent,adminModel={}",adminModel);
                    if(adminModel==null) {
                        adminModel = qyweixinGetAdminListRsp.getAdmin().get(0);
                    }
                    log.info("CorpManager.initCorpEvent,adminModel2={}",adminModel);

                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = qyWeixinManager.getUserInfo(appId,
                            corpAuthInfo.getAuth_corp_info().getCorpid(),
                            adminModel.getUserid());
                    log.info("CorpManager.initCorpEvent,userDetailInfoRsp={}",userDetailInfoRspResult);

                    qyweixinAddEnterprise.setUserId(adminModel.getUserid());

                    if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                        QyweixinUserDetailInfoRsp userDetailInfoRsp = userDetailInfoRspResult.getData();
                        qyweixinAddEnterprise.setUserName(userDetailInfoRsp.getName());
                    } else {
                        qyweixinAddEnterprise.setUserName(adminModel.getUserid());
                    }
                }
            }
        }

        if(StringUtils.isEmpty(qyweixinAddEnterprise.getUserId())) {
            qyweixinAddEnterprise.setUserId(corpAuthInfo.getAuth_user_info().getUserid());
            //成员授权模式下，name可能为null
            qyweixinAddEnterprise.setUserName("U-FSQYWX-"+ (StringUtils.isNotEmpty(corpAuthInfo.getAuth_user_info().getName()) ? corpAuthInfo.getAuth_user_info().getName() : corpAuthInfo.getAuth_user_info().getUserid()));
        }

        qyweixinAddEnterprise.setAvatar(corpAuthInfo.getAuth_user_info().getAvatar());
        qyweixinAddEnterprise.setCorpId(corpAuthInfo.getAuth_corp_info().getCorpid());
        qyweixinAddEnterprise.setCorpName(corpAuthInfo.getAuth_corp_info().getCorp_name());
        qyweixinAddEnterprise.setAppId(appId);

        //设置可见范围 （通讯录应用没有可见范围，做判空处理）
        QyweixinAgentPrivilege qyweixinAgentPrivilege = new QyweixinAgentPrivilege();
        if(null != corpAuthInfo.getAuth_info().getAgent() && ! corpAuthInfo.getAuth_info().getAgent().isEmpty()){
            qyweixinAgentPrivilege.setAllowParty(qyweixinAgentRsp.getPrivilege().getAllow_party());
            qyweixinAgentPrivilege.setAllowTag(qyweixinAgentRsp.getPrivilege().getAllow_tag());

            if(qyweixinAgentRsp.getAuth_mode()==1) {
                qyweixinAgentPrivilege.setAllowUser(Lists.newArrayList(qyweixinAddEnterprise.getUserId()));
            } else {
                qyweixinAgentPrivilege.setAllowUser(qyweixinAgentRsp.getPrivilege().getAllow_user());
            }
        }
        qyweixinAddEnterprise.setPrivilege(qyweixinAgentPrivilege);

        log.info("initCorpEvent,corpAuthInfo.getEdition_info()={}",corpAuthInfo.getEdition_info());
        //判断企业是否是试用，试用则直接推送试用企业开通消息，非试用则进一步查询订单(这里是后来发现问题才多了几个判断的，代码可以进一步优化）
        //增加一个试用过期
        if (null != corpAuthInfo.getEdition_info() && !corpAuthInfo.getEdition_info().getAgent().isEmpty()) {
            if (AppStatusEnum.LIMITED_TIME_TRIAL.getCode().equals(corpAuthInfo.getEdition_info().getAgent().get(0).getApp_status())
                    || AppStatusEnum.UNLIMITED_TRIAL.getCode().equals(corpAuthInfo.getEdition_info().getAgent().get(0).getApp_status())
                    || AppStatusEnum.TRIAL_EXPIRED.getCode().equals(corpAuthInfo.getEdition_info().getAgent().get(0).getApp_status())) {

                qyweixinAddEnterprise.setOrderType(OrderTypeEnum.TRYOUT.getCode());
                // 设置试用版本
                qyweixinAddEnterprise.setEditionId(tryEditionMap.get(appId));
                // 使用默认七天
                long expiredTime = LocalDateTime.now().plusDays(7).toInstant(ZoneOffset.of("+8")).getEpochSecond();
                try {
                    expiredTime = corpAuthInfo.getEdition_info().getAgent().get(0).getExpired_time();
                }catch (Exception ignore){
                    log.warn("try edition get expiredTime failed. corpAuthInfo:{}, qyweixinAddEnterprise:{}", corpAuthInfo, qyweixinAddEnterprise);
                }
                qyweixinAddEnterprise.setEndTime(DateFormatUtils.format(expiredTime * 1000L, "yyyy/MM/dd"));
            } else {
                //查询是否该企业存在未处理的购买订单，存在则合并推送，不存在则只保存企业信息
                QyweixinOrderInfoBo qyweixinOrderInfoBo = new QyweixinOrderInfoBo();
                qyweixinOrderInfoBo.setAppId(appId);
                qyweixinOrderInfoBo.setPaidCorpid(corpAuthInfo.getAuth_corp_info().getCorpid());
                qyweixinOrderInfoBo.setSuiteId(appId);
                qyweixinOrderInfoBo.setProcessingStatus(ProcessingStatusEnum.NOT_PUSHED_UNPROCESSED.getCode());
                qyweixinOrderInfoBo.setOrderFrom(0);//只推送未处理的普通订单，代客下单的除外
                List<QyweixinOrderInfoBo> qyweixinOrderInfo = qyweixinOrderInfoDao.findByEntity(qyweixinOrderInfoBo);
                log.info("CorpManager.initCorpEvent.qyweixinOrderInfo={}",qyweixinOrderInfo);
                if (null != qyweixinOrderInfo && !qyweixinOrderInfo.isEmpty()) {
                    //存在订单信息,合并订单和企业信息
                    log.info("trace createCorpEvent from qywx and found order info, corpid:{}", corpAuthInfo.getAuth_corp_info().getCorpid());
                    qyweixinOrderInfoBo = qyweixinOrderInfo.get(0);
                    qyweixinAddEnterprise = orderManager.convertQyweixinOrderEvent(qyweixinAddEnterprise, qyweixinOrderInfoBo);

                    //置订单处理状态为 已推送已处理
                    qyweixinOrderInfoBo.setProcessingStatus(ProcessingStatusEnum.PUSHED_PROCESSED.getCode());
//                    qyweixinOrderInfoBo.setProcessingStatus(ProcessingStatusEnum.PUSHED_PROCESSED.getCode());
//                    qyweixinOrderInfoBo.setOrderId(qyweixinOrderInfo.get(0).getOrderId());
//                    qyweixinOrderInfoBo.setPaidTime(qyweixinOrderInfo.get(0).getPaidTime());
//                    qyweixinOrderInfoBo.setOrderStatus(qyweixinOrderInfo.get(0).getOrderStatus());
                    log.info("trace new qyweixinOrderInfoBo:{}",qyweixinOrderInfoBo);
                    qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
                    log.info("trace payForAppSuccessEvent save success, orderId{} ", qyweixinOrderInfoBo.getOrderId());
                } else {
                    log.info("trace createCorpEvent from qywx but not found order info, corpid:{}", corpAuthInfo.getAuth_corp_info().getCorpid());
                }
            }
        } else {
            //兼容客户安装完CRM应用后，版本信息字段为空的场景
            qyweixinAddEnterprise.setOrderType(OrderTypeEnum.TRYOUT.getCode());
            // 设置试用版本
            qyweixinAddEnterprise.setEditionId(tryEditionMap.get(appId));
            // 使用默认七天
            long expiredTime = LocalDateTime.now().plusDays(7).toInstant(ZoneOffset.of("+8")).getEpochSecond();
            qyweixinAddEnterprise.setEndTime(DateFormatUtils.format(expiredTime * 1000L, "yyyy/MM/dd"));
            log.info("initCorpEvent,editionInfo=null,qyweixinAddEnterprise={}",qyweixinAddEnterprise);
        }

        orderManager.openEnterprise(qyweixinAddEnterprise);

//        //合并订单和企业信息推送开通企业
//        Message msg = new Message();
//        msg.setTags(TAG_ENTERPRISE);
//        msg.setBody(qyweixinAddEnterprise.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
//        log.info("trace createCorpEvent from qywx send mq msg. authCode:{}, sendResult:{}, message:{}, body:{}",
//                authCode, sendResult, msg, JSONObject.toJSONString(qyweixinAddEnterprise));

        return new Result<>();
    }

    /**
     * 获取管理员userid
     * @param corpId
     */
    public String getAdminUserId(String corpId){
        QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
        qyweixinCorpInfoBo.setCorpId(corpId);
        List<QyweixinCorpInfoBo> qyweixinCorpInfoList = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
        log.info("trace getAdminUserId qyweixinCorpInfoList" + qyweixinCorpInfoList);
        if(null == qyweixinCorpInfoList || qyweixinCorpInfoList.isEmpty()){
            return null;
        } else {
            String userId = qyweixinCorpInfoList.get(0).getUserId();
            return userId;
        }
    }


    /**
     * 保存企业信息，企业绑定信息，应用授权范围
     * @param qyweixinGetPermenantCodeRsp
     * @param appId
     */
    public void saveCorpInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
        String corpId = qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorpid();
        log.info("CorpManager.saveCorpInfoTask,corpId={}",corpId);
        corpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        log.info("CorpManager.saveCorpInfoTask,corpId2={}",corpId);
        //保存绑定关系或更新
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        log.info("CorpManager.saveCorpInfoTask,corpId={},appId={},qyweixinCorpBindBo={}",corpId,appId,qyweixinCorpBindBo);
        if(null == qyweixinCorpBindBo){
            qyweixinCorpBindBo = new QyweixinCorpBindBo();
            qyweixinCorpBindBo.setCorpId(corpId);
            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
            qyweixinCorpBindBo.setAppId(appId);
            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            qyweixinCorpBindDao.save(qyweixinCorpBindBo);
            log.info("CorpManager.saveCorpInfoTask,save success,qyweixinCorpBindBo={}",qyweixinCorpBindBo);
        } else {
            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null :String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
            qyweixinCorpBindDao.update(qyweixinCorpBindBo);
            log.info("CorpManager.saveCorpInfoTask,update success,qyweixinCorpBindBo={}",qyweixinCorpBindBo);
        }

        //保存企业信息或更新
        QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
        qyweixinCorpInfoBo.setCorpId(corpId);
        List<QyweixinCorpInfoBo> qyweixinCorpInfoList = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
        if(null == qyweixinCorpInfoList || qyweixinCorpInfoList.isEmpty()){
            log.warn("trace save qyweixinCorpInfo and query from DB not found info!");
            convertQyweixinCorpInfo( qyweixinGetPermenantCodeRsp , qyweixinCorpInfoBo);
            qyweixinCorpInfoDao.save(qyweixinCorpInfoBo);
        } else {
            log.info("trace save qyweixinCorpInfo and query from DB old info:{}",qyweixinCorpInfoList.get(0));
            QyweixinCorpInfoBo qyweixinCorpInfoDb = qyweixinCorpInfoList.get(0);
            convertQyweixinCorpInfo(qyweixinGetPermenantCodeRsp, qyweixinCorpInfoDb);
            qyweixinCorpInfoDao.update(qyweixinCorpInfoDb);
        }

        //初始化corpAccessToken
        qyWeixinManager.getCorpAccessToken(appId, corpId, true);
    }

    /**
     * 保存企业代开发绑定信息
     * @param qyweixinGetPermenantCodeRsp
     * @param appId
     */
    public void saveRepCorpInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
        String corpId = qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorpid();
        corpId = qyWeixinManager.corpId2OpenCorpId(corpId).getData();
        //保存绑定关系或更新
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        if(null == qyweixinCorpBindBo){
            qyweixinCorpBindBo = new QyweixinCorpBindBo();
            qyweixinCorpBindBo.setCorpId(corpId);
            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
            qyweixinCorpBindBo.setAppId(appId);
            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            int count = qyweixinCorpBindDao.save(qyweixinCorpBindBo);
            log.info("CorpManager.saveRepCorpInfoTask,save,count={},qyweixinCorpBindBo={}",count,qyweixinCorpBindBo);
        } else {
            updateCorpRepSecret(qyweixinCorpBindBo, corpId, qyweixinGetPermenantCodeRsp, appId);
            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null :String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
            int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
            log.info("CorpManager.saveRepCorpInfoTask,update,count={},qyweixinCorpBindBo={}",count,qyweixinCorpBindBo);
        }
        //初始化corpAccessToken
        String corpSecretCode = qyweixinCorpBindDao.getCorpSecretCode(corpId, appId);
        corpSecretCode = SecurityUtil.decryptStr(corpSecretCode);
        qyWeixinManager.getToken(corpSecretCode,corpId);
    }

    public void updateCorpRepSecret(QyweixinCorpBindBo qyweixinCorpBindBo, String corpId,
                                    QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
        //为了区分代开发和自建应用授权的会话存档，在secret未更新的时候做比对
        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa("qywx", corpId,null);
        String ea = result.getData();
        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.querySetting(ea, null, corpId);
        log.info("CorpManager.updateCorpRepSecret,generateSettingVoResult={}", generateSettingVoResult);
        if(ObjectUtils.isNotEmpty(generateSettingVoResult.getData()) &&
                generateSettingVoResult.getData().getCorpSecret().equals(qyweixinCorpBindBo.getPermanentCode())) {
            //更新会话留存的secret和agentId
            GenerateSettingVo generateSettingVo = new GenerateSettingVo();
            generateSettingVo.setQywxCorpId(corpId);
            generateSettingVo.setCorpSecret(qyweixinGetPermenantCodeRsp.getPermanent_code());
            generateSettingVo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null :String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            log.info("CorpManager.updateCorpRepSecret,generateSettingVo={}", generateSettingVo);
            messageGeneratingService.updateCorpRepSecret(generateSettingVo);
        }
    }

    public void convertQyweixinCorpInfo(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, QyweixinCorpInfoBo qyweixinCorpInfoBo) {
        QyweixinAuthCorpInfoRsp qyweixinAuthCorpInfoRsp = qyweixinGetPermenantCodeRsp.getAuth_corp_info();
        qyweixinCorpInfoBo.setCorpName(qyweixinAuthCorpInfoRsp.getCorp_name());
        qyweixinCorpInfoBo.setAppLevel(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? "1" : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getLevel()));
        qyweixinCorpInfoBo.setCorpFullName(qyweixinAuthCorpInfoRsp.getCorp_full_name());
        qyweixinCorpInfoBo.setCorpIndustry(qyweixinAuthCorpInfoRsp.getCorp_industry());

        if(null != qyweixinGetPermenantCodeRsp.getAuth_user_info()){
            qyweixinCorpInfoBo.setUserId(qyweixinGetPermenantCodeRsp.getAuth_user_info().getUserid());
        }
        qyweixinCorpInfoBo.setCorpScale(qyweixinAuthCorpInfoRsp.getCorp_scale());
        qyweixinCorpInfoBo.setCorpSquareLogoUrl(qyweixinAuthCorpInfoRsp.getCorp_square_logo_url());
        qyweixinCorpInfoBo.setCorpSubIndustry(qyweixinAuthCorpInfoRsp.getCorp_sub_industry());
        qyweixinCorpInfoBo.setCorpType(qyweixinAuthCorpInfoRsp.getCorp_type());
        qyweixinCorpInfoBo.setCorpUserMax(qyweixinAuthCorpInfoRsp.getCorp_user_max());
        qyweixinCorpInfoBo.setCorpWxqrcode(qyweixinAuthCorpInfoRsp.getCorp_wxqrcode());
        qyweixinCorpInfoBo.setSubjectType(qyweixinAuthCorpInfoRsp.getSubject_type());
        qyweixinCorpInfoBo.setVerifiedEndTime(qyweixinAuthCorpInfoRsp.getVerified_end_time());
        qyweixinCorpInfoBo.setLocation(qyweixinAuthCorpInfoRsp.getLocation());

        //在线订单上线后加上获取企业当前版本信息
        if (null != qyweixinGetPermenantCodeRsp.getEdition_info() && !qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().isEmpty()) {
            log.info("trace convertQyweixinCorpInfo convert editionInfo:{}", qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0));
            qyweixinCorpInfoBo.setEditionId(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getEdition_id());
            qyweixinCorpInfoBo.setEditionName(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getEdition_name());
            qyweixinCorpInfoBo.setAppStatus(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getApp_status());
            qyweixinCorpInfoBo.setUserLimit(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getUser_limit());
            qyweixinCorpInfoBo.setExpiredTime(qyweixinGetPermenantCodeRsp.getEdition_info().getAgent().get(0).getExpired_time());
        }

    }

    public String getValidCorpId(String corpId, String appId) {
        String oldCorpId = appId;
        String validCorpId = corpId;
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        if(qyweixinCorpBindBo==null) {
            qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, repAppId);
            if(qyweixinCorpBindBo!=null) {
                QyweixinCorpBindBo qyweixinCorpBindBo2 = qyweixinCorpBindDao.queryQyweixinCorpBind(qyweixinCorpBindBo.getCorpId(), appId);
                if(qyweixinCorpBindBo2!=null) {
                    validCorpId = qyweixinCorpBindBo.getCorpId();
                } else {
                    if(StringUtils.isNotEmpty(qyweixinCorpBindBo.getIsvCorpId())) {
                        qyweixinCorpBindBo2 = qyweixinCorpBindDao.queryQyweixinCorpBind(qyweixinCorpBindBo.getIsvCorpId(), appId);
                        if(qyweixinCorpBindBo2!=null) {
                            validCorpId = qyweixinCorpBindBo.getIsvCorpId();
                        }
                    }
                }
            }
        } else {
            validCorpId = qyweixinCorpBindBo.getCorpId();
        }
        log.info("CorpManager.getValidCorpId,corpId={},appId={},validCorpId={}",oldCorpId,appId,validCorpId);
        return validCorpId;
    }

    /**
     * 获取缓存永久授权码
     * @param corpId
     * @param appId
     * @return
     */
    public String getPermannentCodeFromDB(String corpId, String appId) {
        if(StringUtils.isBlank(corpId)){
            //throw new RuntimeException("trace getPermannentCodeFromDB Required String parameter 'corpId' is not present");
            log.info("trace getPermannentCodeFromDB Required String parameter 'corpId' is not present");
            return "";
        }
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        log.info("CorpManager.getPermannentCodeFromDB,qyweixinCorpBindBo={}",qyweixinCorpBindBo);
        if(null == qyweixinCorpBindBo){
            throw new RuntimeException("trace getPermannentCodeFromDB not find permannent_code corpId:"+corpId + " appId:"+appId);
        }
        return qyweixinCorpBindBo.getPermanentCode();
    }

    /**
     * 获取缓存永久授权码
     * @param corpId
     * @param appId
     * @return
     */
    public String getPermannentCodeFromDB2(String corpId, String appId) {
        if(StringUtils.isBlank(corpId)){
            //throw new RuntimeException("trace getPermannentCodeFromDB Required String parameter 'corpId' is not present");
            log.info("trace getPermannentCodeFromDB2 Required String parameter 'corpId' is not present");
            return "";
        }
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        log.info("CorpManager.getPermannentCodeFromDB2,qyweixinCorpBindBo={}",qyweixinCorpBindBo);
        if(null == qyweixinCorpBindBo){
            return "";
        }
        return qyweixinCorpBindBo.getPermanentCode();
    }

    public String getFsEaByOutEa(String corpId) {
        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa(SourceTypeEnum.QYWX.getSourceType(),
                corpId,null);
        if(! result.isSuccess()){
            log.error("trace getFsEaByOutEa error corpId:{} ", corpId);
            return null;
        }
        return result.getData();
    }

    public void updateCorpInfo(QyweixinGetAuthInfoRsp authInfoRsp, String corpId, String appId) {

        //检测到是手动绑定纷享企业的，不发送消息
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
            //只有手动绑定场景，才支持一个企业微信对多个CRM
            QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = enterpriseMappingList.get(0);
            if( null != qyweixinAccountEnterpriseMapping
                    && QyweixinBindTypeEnum.OLD_CORP_BIND.getCode().equals(qyweixinAccountEnterpriseMapping.getBindType())
                    && !ConfigCenter.EMPLOYEE_SYNC_EA.contains(qyweixinAccountEnterpriseMapping.getFsEa())){
                log.info("trace qyweixinAccountEnterpriseMapping is:{} 手动绑定不发送消息", QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
                return ;
            }
        }

        QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp = new QyweixinGetPermenantCodeRsp();
        BeanUtils.copyProperties(authInfoRsp, qyweixinGetPermenantCodeRsp);

        //保存企业信息或更新
        QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
        qyweixinCorpInfoBo.setCorpId(corpId);
        List<QyweixinCorpInfoBo> qyweixinCorpInfoList = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
        if(null == qyweixinCorpInfoList || qyweixinCorpInfoList.isEmpty()){
            log.error("updateCorpInfo get errmsg: trace_appId:{}, corpId:{}, 更新授权信息时, 没有查到企业信息 ", appId, corpId);
            return;
        }
        QyweixinCorpInfoBo qyweixinCorpInfoDb = qyweixinCorpInfoList.get(0);
        convertQyweixinCorpInfo(qyweixinGetPermenantCodeRsp, qyweixinCorpInfoDb);
        qyweixinCorpInfoDao.update(qyweixinCorpInfoDb);

        //应用可见范围变更处理逻辑入口
        contactsService.onAvailableRangeChanged(appId,corpId);

//        QyweixinUpdateOrganizationEvent updateOrganizationEvent = new QyweixinUpdateOrganizationEvent();
//        updateOrganizationEvent.setAppId(appId);
//        updateOrganizationEvent.setCorpId(corpId);
//        updateOrganizationEvent.setSource(SourceTypeEnum.QYWX.getSourceType());
//        updateOrganizationEvent.setFsEa(getFsEaByOutEa(corpId));
//        updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_APP_PRIVILEGE.getEventType());
//
//        updateOrganizationEvent.setAllowParty(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getAllow_party());
//        updateOrganizationEvent.setAllowUser(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getAllow_user());
//        updateOrganizationEvent.setAllowTag(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getPrivilege().getAllow_tag());
//
//        Message msg = new Message();
//        msg.setTags(TAG_ORGANIZATION);
//        msg.setBody(updateOrganizationEvent.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
//        log.info("trace_appId:{} changeContacts from qywx send mq msg sendResult:{}, message:{}, body:{}", appId, sendResult, msg, JSONObject.toJSONString(updateOrganizationEvent));
    }

    public void changeContacts(String plainMsg, String changeType) {

        //String fsAccount="";
        //通讯录变更通知通讯录变更事件(员工、部门、标签)  发送变更消息
        ContactsXml contactsInfo = XStreamUtils.parseXml(plainMsg, ContactsXml.class);
        //String appId = contactsInfo.getSuiteId();

        //通讯录员工变动
        if("create_user".contains(changeType) || "update_user".equals(changeType) || "delete_user".equals(changeType)) {
            sendChangeContactsMsg(contactsInfo, changeType);
        }

        contactsService.onChangeContacts(contactsInfo);

//        //判断是否是更新userId，然后查找数据库是否存在这个out_account,是则更新,否则插入
//        if (null != contactsInfo.getNewUserID() && !contactsInfo.getNewUserID().isEmpty()){
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountEmployeeMappingList =
//                    qyweixinAccountBindService.queryFsAccountBindByOldOutAccount(SourceTypeEnum.QYWX.getSourceType(),
//                            Lists.newArrayList(contactsInfo.getUserID()), appId, contactsInfo.getAuthCorpId());
//            if(null != accountEmployeeMappingList) {
//                log.info("trace queryFsEaBindByOldOutAccount accountEmployeeMappingList:{}", accountEmployeeMappingList);
//                fsAccount = accountEmployeeMappingList.getData().get(0).getFsAccount();
//                // 所有应用一起更新
//                int result = qyweixinAccountBindService.updateByNewOutAccount(contactsInfo.getNewUserID(),
//                        contactsInfo.getUserID(), StringUtils.EMPTY, contactsInfo.getAuthCorpId());
//                log.info("trace updateByNewOutAccount (success：1，fail：0） result:{}", result);
//            }else {
//                changeType="create_user";
//            }
//        }
//
//        //检测到是手动绑定纷享企业的，不发送消息
////        QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(contactsInfo.getAuthCorpId());
////        if( null != qyweixinAccountEnterpriseMapping && QyweixinBindTypeEnum.OLD_CORP_BIND.getCode().equals(qyweixinAccountEnterpriseMapping.getBindType())){
////            log.info("trace qyweixinAccountEnterpriseMapping is:{} 手动绑定不发送消息", QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
////            return ;
////        }
//        if(qyweixinAccountBindService.isManualBinding("", contactsInfo.getAuthCorpId()).getData()) {
//            log.info("CorpManager.changeContacts,corpId={},boolean={}.", contactsInfo.getAuthCorpId(), qyweixinAccountBindService.isManualBinding("", contactsInfo.getAuthCorpId()).getData());
//            return;
//        }
//
//        QyweixinUpdateOrganizationEvent updateOrganizationEvent = new QyweixinUpdateOrganizationEvent();
//        updateOrganizationEvent.setAppId(appId);
//        updateOrganizationEvent.setCorpId(contactsInfo.getAuthCorpId());
//        updateOrganizationEvent.setSource(SourceTypeEnum.QYWX.getSourceType());
//        updateOrganizationEvent.setFsEa(getFsEaByOutEa(contactsInfo.getAuthCorpId()));
//
//        Message msg = new Message();
//        // 延时1s投递
//        msg.setDelayTimeLevel(1);
//        msg.setTags(TAG_ORGANIZATION);
//        if("create_user".contains(changeType)){
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_EMPLOYEE.getEventType());
//            updateOrganizationEvent.setEventId(StringUtils.isEmpty(contactsInfo.getNewUserID()) ? contactsInfo.getUserID(): contactsInfo.getNewUserID());
//        }else if("update_user".equals(changeType)){  //修改了userid的把fsAccount传过去就不用再调用sync-provider的方法了
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.MODIFY_EMPLOYEE.getEventType());
//            updateOrganizationEvent.setEventId(StringUtils.isEmpty(contactsInfo.getNewUserID()) ? contactsInfo.getUserID(): contactsInfo.getNewUserID());
//            updateOrganizationEvent.setFsAccount(fsAccount);
//        } else if("delete_user".equals(changeType)){
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.DELETE_EMPLOYEE.getEventType());
//            updateOrganizationEvent.setEventId(contactsInfo.getUserID());
//        } else if("create_party,update_party".contains(changeType)){
//
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_DEPARTMENT.getEventType());
//            updateOrganizationEvent.setEventId(contactsInfo.getId());
//        }else if("delete_party".equals(changeType)){
//
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.DELETE_DEPARTMENT.getEventType());
//            updateOrganizationEvent.setEventId(contactsInfo.getId());
//        }else if("update_tag".equals(changeType)){
//
//            //标签变更 发送部门员工变更消息
//            String addUser = contactsInfo.getAddUserItems();
//            String addParty = contactsInfo.getAddPartyItems();
//            String delUser = contactsInfo.getDelUserItems();
//            String delParty = contactsInfo.getDelPartyItems();
//
//            updateOrganizationEvent.setEventId(contactsInfo.getTagId());
//            updateOrganizationEvent.setEventType(QyweixinUpdateEventTypeEnum.UPDATE_TAG.getEventType());
//            updateOrganizationEvent.setAddAllowParty(null == addParty ? Lists.newArrayList() : Arrays.stream(addParty.split(",")).collect(Collectors.toList()));
//            updateOrganizationEvent.setReduceAllowParty(null == delParty ? Lists.newArrayList() : Arrays.stream(delParty.split(",")).collect(Collectors.toList()));
//            updateOrganizationEvent.setAddAllowUser(null == addUser ? Lists.newArrayList() : Arrays.stream(addUser.split(",")).collect(Collectors.toList()));
//            updateOrganizationEvent.setReduceAllowUser(null == delUser ? Lists.newArrayList() : Arrays.stream(delUser.split(",")).collect(Collectors.toList()));
//        }
//
//        msg.setBody(updateOrganizationEvent.toProto());
//        SendResult sendResult = qywxEventNotifyMQSender.send(msg);
//        log.info("trace_appId:{} changeContacts from qywx send mq msg sendResult:{}, message:{}, body:{}",contactsInfo.getSuiteId(), sendResult, msg, JSONObject.toJSONString(updateOrganizationEvent));

    }

    private void sendChangeContactsMsg(ContactsXml contactsInfo, String changeType) {
        String corpId = contactsInfo.getAuthCorpId();
        //查询企业绑定关系
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            log.info("CorpManager.sendChangeContactsMsg,enterprise not bind.corpId={}", contactsInfo.getAuthCorpId());
            return;
        }
        ChangeContactEvent event = new ChangeContactEvent();
        event.setAppId(contactsInfo.getSuiteId());
        event.setChangeType(changeType);
        event.setCorpId(corpId);
        //查询员工绑定关系
        String userId = StringUtils.isEmpty(contactsInfo.getNewUserID()) ? contactsInfo.getUserID(): contactsInfo.getNewUserID();
        event.setUserId(userId);
        for(QyweixinAccountEnterpriseMapping mapping : enterpriseMappingList) {
            event.setFsEa(mapping.getFsEa());
            Result<String> result = qyweixinAccountBindInnerService.outAccountToFsAccount("qywx", corpId, userId, mapping.getFsEa());
            log.info("CorpManager.sendChangeContactsMsg,result={}",result);
            //人员没有绑定关系，也发送
            if(StringUtils.isNotEmpty(result.getData())) {
                event.setFsUserId(result.getData());
            }

            Message msg = new Message();
            msg.setTags("create_user".contains(changeType) ? TAG_CREATE_USER : ("update_user".equals(changeType) ? TAG_UPDATE_USER : TAG_DELETE_USER));
            msg.setBody(event.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(mapping.getFsEa())) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.qywxEventNotifyMQSender.name());
                cloudMessageProxyProto.setCorpId(corpId);
                cloudMessageProxyProto.setFsEa(mapping.getFsEa());
                cloudMessageProxyProto.setMessage(msg);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(mapping.getFsEa()), cloudMessageProxyProto);
            } else {
                SendResult sendResult = qywxEventNotifyMQSender.send(msg);
                log.info("CorpManager.sendChangeContactsMsg,ea={},sendResult={}", mapping.getFsEa(), sendResult);
            }
        }
    }

    public void changeContacts2(String plainMsg, String event,String appId) {
        log.info("CorpManager.changeContacts2,plainMsg={},event={},appId={}",plainMsg,
                event,appId);
        QYWeiXinMemberEventXml memberEventXml = XStreamUtils.parseXml(plainMsg, QYWeiXinMemberEventXml.class);
        log.info("CorpManager.changeContacts2,memberEventXml={}",memberEventXml);

        String outEa = memberEventXml.getToUserName();
        String outUserId = memberEventXml.getFromUserName();

        //检测到是手动绑定纷享企业的，不发送消息
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(outEa);
        log.info("CorpManager.changeContacts2,enterpriseMappingList={}",enterpriseMappingList);
        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
            QyweixinAccountEnterpriseMapping qyweixinAccountEnterpriseMapping = enterpriseMappingList.get(0);
            if( null != qyweixinAccountEnterpriseMapping && QyweixinBindTypeEnum.OLD_CORP_BIND.getCode().equals(qyweixinAccountEnterpriseMapping.getBindType())){
                log.info("CorpManager.changeContacts2,手动绑定不发送消息");
                return ;
            }
        } else {
            return;
        }

        Result<List<QyweixinAccountEmployeeMapping>> employeeMapping = qyweixinAccountBindInnerService.getEmployeeMapping(outEa,
                outUserId,
                -1,
                enterpriseMappingList.get(0).getFsEa());
        log.info("CorpManager.changeContacts2,employeeMapping={}",employeeMapping);
        //CRM应用可见范围新增人员或者可见范围内的部门下新增员工
        if("subscribe".contains(event)) {
            //如果员工绑定关系不存在，新增员工并建立绑定关系，如果员工被停用，则恢复员工状态
            contactsService.addUserList(appId, outEa, Lists.newArrayList(outUserId));
        } else if("unsubscribe".equals(event)) { //从应用可见范围移除员工或者可见范围内的部门下移除员工或者员工离职
            if(CollectionUtils.isNotEmpty(employeeMapping.getData())) {
                //如果员工绑定关系存在，停用员工并更新绑定关系
                contactsService.stopUserList(appId, outEa, Lists.newArrayList(outUserId));
            }
        }
    }

    public com.facishare.open.qywx.accountbind.result.Result<Boolean> saveEnterpriseAccountBind(String outEa,
                                                                                                String depId,
                                                                                                String fsEa,
                                                                                                String corpName,
                                                                                                String isvCorpId) {
        QyweixinAccountEnterpriseMapping accountEnterpriseMapping = new QyweixinAccountEnterpriseMapping();
        accountEnterpriseMapping.setFsEa(fsEa);
        accountEnterpriseMapping.setOutEa(outEa);
        accountEnterpriseMapping.setDepId(depId);
        accountEnterpriseMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
        accountEnterpriseMapping.setStatus(0);
        accountEnterpriseMapping.setBindType(QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
        accountEnterpriseMapping.setOutName(corpName);
        accountEnterpriseMapping.setIsvOutEa(isvCorpId);
        accountEnterpriseMapping.setDomain(ConfigCenter.crm_domain);
        Timestamp timestamp=new Timestamp(new Date().getTime());
        accountEnterpriseMapping.setGmtCreate(timestamp);
        accountEnterpriseMapping.setGmtModified(timestamp);
        return qyweixinAccountBindService.bindAccountEnterpriseMapping(accountEnterpriseMapping);
    }

    /**
     * 异步通知同步历史待办消息 审批流，工作流消息
     * @param fsEa
     */
    public void notifyHistoryToDoMessage(String fsEa) {
        corpManagerThreadPool.execute(()->{
            try {
                int fsEi = eieaConverter.enterpriseAccountToId(fsEa);
                //同步历史待办消息
                Map<String, String> historyHeadersMap = Maps.newHashMap();
                historyHeadersMap.put("x-fs-ei", String.valueOf(fsEi));
                historyHeadersMap.put("x-fs-userinfo", "-10000");
                //限速
                if(!CrmRateLimiter.isAllowed(null)) {
                    return;
                }
                String historyToDoMessageResult = httpHelper.doGet(historyToDoMessageUrl + fsEi, historyHeadersMap);
                //成功返回：{"value":{"Result":true},"success":true,"message":"","errorCode":0}
                JSONObject historyToDoMessageJsonObject = JSONObject.parseObject(historyToDoMessageResult);
                if(0 == historyToDoMessageJsonObject.getIntValue("errorCode")){
                    if(historyToDoMessageJsonObject.getJSONObject("value").getBoolean("Result")){
                        log.info("trace notifyHistoryToDoMessage_success ei:{} ea:{}", fsEi, fsEa);
                    } else {
                        log.error("trace notifyHistoryToDoMessage_error ei:{} ea:{} result:{}", fsEi, fsEa, historyToDoMessageResult);
                    }
                } else{
                    log.error("trace notifyHistoryToDoMessage_error ei:{} ea:{} result:{}", fsEi, fsEa, historyToDoMessageResult);
                }


                Map<String, String> flowTaskMap = Maps.newHashMap();
                flowTaskMap.put("x-tenant-id", String.valueOf(fsEi));
                flowTaskMap.put("x-user-id", "-10000");
                flowTaskMap.put("Content-type", "application/json");
                //限速
                if(!CrmRateLimiter.isAllowed(null)) {
                    return;
                }
                String flowTaskResult = httpHelper.postJsonData2(flowTaskUrl, null, flowTaskMap);
                //成功返回：{"code":0,"message":"","data":"ok"}
                JSONObject flowTaskJsonObject = JSONObject.parseObject(flowTaskResult);
                if( 0 == flowTaskJsonObject.getIntValue("code")){
                    log.info("trace notifyHistoryToDoMessage_FlowTask_success ei:{} ea:{}", fsEi , fsEa);
                } else {
                    log.error("trace notifyHistoryToDoMessage_FlowTask_success ei:{} ea:{} result:{}", fsEi , fsEa, flowTaskResult);
                }
            } catch (RemoteException e) {
                log.info("trace notifyHistoryToDoMessage_error RemoteException fsEa:{} e:{}", fsEa, e.getMessage());
            } catch (Exception e){
                log.info("trace notifyHistoryToDoMessage_error Exception fsEa:{} e:{} ", fsEa, e.getMessage());
            }
        });
    }

    /**
     * 给绑定渠道的用户发送欢迎消息
     * 场景1 先安装CRM > 安装通讯录 > 给CRM可见范围的员工发送消息
     * 场景2 先安装通讯录 > 再在安装CRM >  给CRM可见范围的员工发送消息
     *
     * @param userIds
     * @param fsEa
     * @param corpId
     */
    private void sendMsg(List<String> userIds, String fsEa, String corpId) {

        if(StringUtils.isBlank(fsEa) || StringUtils.isBlank(corpId) || null ==userIds || userIds.isEmpty()){
            log.info("trace sendMsg empty parameter fsEa:{} corpId:{} userIds:{}", fsEa, corpId, userIds);
            return;
        }
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
        corpManagerThreadPool.execute(()->{
            QyweixinSendMsgEvent qyweixinSendMsgEvent = new QyweixinSendMsgEvent();
            qyweixinSendMsgEvent.setAppId(mainAppId);
            qyweixinSendMsgEvent.setCorpId(corpId);
            qyweixinSendMsgEvent.setFsEa(fsEa);
            qyweixinSendMsgEvent.setToUserList(userIds);
            Message msg = new Message();
            msg.setTags(TAG_MESSAGE);
            msg.setBody(qyweixinSendMsgEvent.toProto());
            SendResult sendResult = qywxEventNotifyMQSender.send(msg);
            log.info("trace_appId:{} sendMsg from qywx send mq msg sendResult:{}, message:{}, body:{} fsEa:{}",
                    mainAppId, sendResult, msg, JSONObject.toJSONString(qyweixinSendMsgEvent), fsEa);
        });
    }

    /**
     * 场景：在安装CRM应用时 》 给绑定通讯录应用的员工发欢迎消息
     * 该方法业务：检测安装了通讯录应用 》 全量给已绑定的用户发欢迎消息
     * @param corpId
     * @param fsEa
     */
    public void sendMsgAfterInstallCRM(String corpId, String fsEa) {
        try{
            String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
            QyweixinCorpBindBo contactAppBind = new QyweixinCorpBindBo();
            contactAppBind.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            contactAppBind.setCorpId(corpId);
            contactAppBind.setAppId(contactAppId);

            List<QyweixinCorpBindBo> findContactBindResult = qyweixinCorpBindDao.findByEntity(contactAppBind);
            if(null != findContactBindResult && !findContactBindResult.isEmpty()){
                List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings = qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa,mainAppId, corpId);
                sendMsg(qyweixinAccountEmployeeMappings.stream().map(v-> v.getFsAccount().substring(v.getFsAccount().lastIndexOf(".")+1)).collect(Collectors.toList()), fsEa, corpId);
            } else {
                log.info("trace sendMsgAfterInstallCRM_error not install contactApp corpId:{} fsEa:{}", corpId, fsEa);
            }
        }catch (Exception e){
            log.error("trace sendMsgAfterInstallCRM exception corpId:"+corpId + " fsEa:" +fsEa, e);
        }

    }

    /**
     * 场景：安装通讯录后 》 绑定账号 触发发送欢迎消息
     * 该方法业务：检测安装了CRM发送消息 》 给已绑定的用户发欢迎消息
     * @param bindList
     * @param corpId
     * @param fsEa
     */
    public void sendMsgAfterAccountBind(List<String> bindList, String corpId, String fsEa) {
        try{
            String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
            QyweixinCorpBindBo crmAppBind = new QyweixinCorpBindBo();
            crmAppBind.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            crmAppBind.setCorpId(corpId);
            crmAppBind.setAppId(mainAppId);

            List<QyweixinCorpBindBo> findCrmBindResult = qyweixinCorpBindDao.findByEntity(crmAppBind);
            if(null != findCrmBindResult && !findCrmBindResult.isEmpty()){
                sendMsg(bindList.stream().map(v -> {
                    String[] bindInfoArray = v.split("&");
                    if (!StringUtils.isBlank(bindInfoArray[0]) && !StringUtils.isBlank(bindInfoArray[1])) {
                        return bindInfoArray[0];
                    } else {
                        return null;
                    }
                }).filter(x -> x != null).collect(Collectors.toList()), fsEa, corpId);
            } else {
                log.info("trace sendMsgAfterAccountBind_error not install CRM corpId:{} fsEa:{}", corpId, fsEa);
            }

        }catch (Exception e){
            log.error("trace sendMsgAfterAccountBind exception corpId:"+corpId + " fsEa:" +fsEa, e);
        }

    }

    public void saveQyweixinAdminMobile(QyweixinUserDetailInfoRsp userDetailInfoRsp, String appId) {
        log.info("saveQyweixinAdminMobile start. userDetailInfoRsp:{}, appId:{}", userDetailInfoRsp, appId);
        if (StringUtils.isEmpty(userDetailInfoRsp.getCorpid())) return;
        corpManagerThreadPool.execute(()->{
            QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
            qyweixinCorpInfoBo.setCorpId(userDetailInfoRsp.getCorpid());
            List<QyweixinCorpInfoBo> corpInfo = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
            if( null ==corpInfo || corpInfo.isEmpty()){
                log.info("trace saveQyweixinAdminMobile get corpInfo empty corpId:{}", userDetailInfoRsp.getCorpid());
                return;
            }
            log.info("trace saveQyweixinAdminMobile createApp admin user_id:{}  login_user_id:{}", corpInfo.get(0).getUserId(), userDetailInfoRsp.getUserid());
            if(StringUtils.isBlank(corpInfo.get(0).getUserId())){
                log.error("trace saveQyweixinAdminMobile create app's admin userId is empty corpId:{}", userDetailInfoRsp.getCorpid());
                return;
            }
            if(userDetailInfoRsp.getUserid().equals(corpInfo.get(0).getUserId())){
                PlatformAppAdminInfoBo platformAppAdminInfoBo = new PlatformAppAdminInfoBo();
                platformAppAdminInfoBo.setSource(SourceTypeEnum.QYWX.getSourceType());
                platformAppAdminInfoBo.setAppId(appId);
                platformAppAdminInfoBo.setOutEa(userDetailInfoRsp.getCorpid());
                platformAppAdminInfoBo.setMobile(userDetailInfoRsp.getMobile());
                platformAppAdminInfoBo.setName(userDetailInfoRsp.getName());

                int count = platformAppAdminInfoDao.savePlatformAdminInfo(platformAppAdminInfoBo);
                log.info("trace saveQyweixinAdminMobile success updateCount:{}", count);
            }
        });
    }

    public boolean isManualBinding(String corpId) {
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
            if(null != enterpriseMapping && QyweixinBindTypeEnum.OLD_CORP_BIND.getCode().equals(enterpriseMapping.getBindType())){
                log.info("isManualBinding=true, 手动绑定不发送消息", QyweixinBindTypeEnum.OLD_CORP_BIND.getCode());
                return true;
            }
        }
        return false;
    }

    /**
     * 保存代开发应用授权
     * @param qyweixinGetPermenantCodeRsp
     * @param appId
     */
    public void saveRepInfoTask(QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp, String appId) {
        log.info("CorpManager.saveRepInfoTask,qyweixinGetPermenantCodeRsp={},appId={}", qyweixinGetPermenantCodeRsp, appId);
        String corpId = qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorpid();
//        //明文corpId转换为加密的corpId
//        String isvCorpId = qyWeixinManager.getIsvCorpId(corpId);
        String isvCorpId = corpId;
        //保存绑定关系或更新
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        if (null == qyweixinCorpBindBo) {
            qyweixinCorpBindBo = new QyweixinCorpBindBo();
            qyweixinCorpBindBo.setCorpId(corpId);
            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
            qyweixinCorpBindBo.setAppId(appId);
            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            qyweixinCorpBindBo.setIsvCorpId(isvCorpId);
            int count = qyweixinCorpBindDao.save(qyweixinCorpBindBo);
            log.info("CorpManager.saveRepInfoTask,save,count={},qyweixinCorpBindBo={}", count,qyweixinCorpBindBo);
            //第一次授权代开发时发送mq
            String ea = qyweixinCorpBindDao.getFsEaByOutEa(corpId, ConfigCenter.crm_domain);
            log.info("CorpManager.saveRepInfoTask,ea={}", ea);
            if(StringUtils.isNotEmpty(ea)) {
                messageGeneratingService.sendMessage(ea);
            }
        } else {
            qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.BIND.getCode());
            qyweixinCorpBindBo.setCorpName(qyweixinGetPermenantCodeRsp.getAuth_corp_info().getCorp_name());
            qyweixinCorpBindBo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            qyweixinCorpBindBo.setPermanentCode(qyweixinGetPermenantCodeRsp.getPermanent_code());
            qyweixinCorpBindBo.setIsvCorpId(isvCorpId);
            int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
            log.info("CorpManager.saveRepInfoTask,update,count={},qyweixinCorpBindBo={}",count,qyweixinCorpBindBo);
        }
        //判断会话存档是否需要保存代开发的corpSecret和agentId
        this.updateCorpMessageGenerating(corpId, qyweixinGetPermenantCodeRsp);
        qyWeixinManager.getToken(qyweixinGetPermenantCodeRsp.getPermanent_code(), corpId);
    }

    public void saveOrUpdateCorpBindData(List<QyweixinCorpBindBo> corpBindBoList) {
        for(QyweixinCorpBindBo corpBindBo : corpBindBoList) {
            QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpBindBo.getCorpId(), corpBindBo.getAppId());
            if (null == qyweixinCorpBindBo) {
                qyweixinCorpBindBo = new QyweixinCorpBindBo();
                qyweixinCorpBindBo.setCorpId(corpBindBo.getCorpId());
                qyweixinCorpBindBo.setCorpName(corpBindBo.getCorpName());
                qyweixinCorpBindBo.setAppId(corpBindBo.getAppId());
                qyweixinCorpBindBo.setPermanentCode(corpBindBo.getPermanentCode());
                qyweixinCorpBindBo.setAgentId(corpBindBo.getAgentId());
                qyweixinCorpBindBo.setStatus(corpBindBo.getStatus());
                qyweixinCorpBindBo.setIsvCorpId(corpBindBo.getIsvCorpId());
                int count = qyweixinCorpBindDao.save(qyweixinCorpBindBo);
                log.info("CorpManager.saveRepInfoTask,save,count={},corpBindBo={}", count,corpBindBo);
            } else {
                corpBindBo.setId(qyweixinCorpBindBo.getId());
                int count = qyweixinCorpBindDao.update(corpBindBo);
                log.info("CorpManager.saveRepInfoTask,update,count={},corpBindBo={}",count,corpBindBo);
            }
        }
    }

    public void saveOrUpdateCorpInfoData(List<QyweixinCorpInfoBo> corpInfoBoList) {
        for(QyweixinCorpInfoBo corpInfoBo : corpInfoBoList) {
            //保存企业信息或更新
            QyweixinCorpInfoBo qyweixinCorpInfoBo = new QyweixinCorpInfoBo();
            qyweixinCorpInfoBo.setCorpId(corpInfoBo.getCorpId());
            List<QyweixinCorpInfoBo> qyweixinCorpInfoList = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);
            if(null == qyweixinCorpInfoList || qyweixinCorpInfoList.isEmpty()){
                int save = qyweixinCorpInfoDao.save(corpInfoBo);
                log.info("CorpManager.saveOrUpdateCorpInfoData,save,count={},corpInfoBo={}", save,corpInfoBo);
            } else {
                int update = qyweixinCorpInfoDao.update(corpInfoBo);
                log.info("CorpManager.saveOrUpdateCorpInfoData,update,count={},corpInfoBo={}", update,corpInfoBo);
            }
        }
    }

    public void updateCorpMessageGenerating(String corpId, QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp) {
        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa("qywx", corpId,null);
        String ea = result.getData();
        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.querySetting(ea, null, corpId);
        if(ObjectUtils.isNotEmpty(generateSettingVoResult.getData()) &&
                StringUtils.isEmpty(generateSettingVoResult.getData().getCorpSecret()) &&
                StringUtils.isEmpty(generateSettingVoResult.getData().getAgentId())) {
            GenerateSettingVo generateSettingVo = new GenerateSettingVo();
            generateSettingVo.setQywxCorpId(corpId);
            generateSettingVo.setCorpSecret(qyweixinGetPermenantCodeRsp.getPermanent_code());
            generateSettingVo.setAgentId(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().isEmpty() ? null : String.valueOf(qyweixinGetPermenantCodeRsp.getAuth_info().getAgent().get(0).getAgentid()));
            log.info("CorpManager.updateCorpMessageGenerating,generateSettingVo={}", generateSettingVo);
            messageGeneratingService.updateCorpRepSecret(generateSettingVo);
        }
    }

    public void updateCorpMessageGenerating(String corpId, QyweixinCorpBindBo qyweixinCorpBindBo) {
        com.facishare.open.qywx.accountbind.result.Result<String> result = qyweixinAccountBindService.outEaToFsEa("qywx", corpId,null);
        String ea = result.getData();
        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.querySetting(ea, null, corpId);
        if(ObjectUtils.isNotEmpty(generateSettingVoResult.getData()) && qyweixinCorpBindBo.getPermanentCode().equals(generateSettingVoResult.getData().getCorpSecret())) {
            GenerateSettingVo generateSettingVo = new GenerateSettingVo();
            generateSettingVo.setQywxCorpId(corpId);
            generateSettingVo.setCorpSecret(null);
            generateSettingVo.setAgentId(null);
            log.info("CorpManager.updateCorpMessageGenerating,generateSettingVo={}", generateSettingVo);
            messageGeneratingService.updateCorpRepSecret(generateSettingVo);
        }
    }

//    public void autoBindAccountEnterprise(String fsEa, String corpId, List<String> userIds) {
//        log.info("CorpManager.autoBindAccountEnterprise,fsEa={},corpId={},userIds={}.", fsEa, corpId, userIds);
//        List<QyweixinAccountEmployeeMapping> employeeMappingList = new LinkedList<>();
//        for(String userId : userIds) {
//            QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
//            //通过代开发获取名称
//            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> qyweixinUserInfoResult = qyWeixinManager.getUserInfo(repAppId, corpId, userId);
//            log.info("CorpManager.autoBindAccountEnterprise,qyweixinUserInfo={}.", qyweixinUserInfoResult);
//            if(!qyweixinUserInfoResult.isSuccess() || ObjectUtils.isEmpty(qyweixinUserInfoResult.getData())) {
//                continue;
//            }
//            QyweixinUserDetailInfoRsp qyweixinUserInfo = qyweixinUserInfoResult.getData();
//            //通过名称获取纷享CRM的员工的详情
//            GetEmployeesDtoByNameResult fsEmployeeInfo = fsManager.getEmployeesByName(fsEa, qyweixinUserInfo.getName());
//            log.info("CorpManager.autoBindAccountEnterprise,fsEmployeeInfo={}.", fsEmployeeInfo);
//            if(ObjectUtils.isEmpty(fsEmployeeInfo) || ObjectUtils.isEmpty(fsEmployeeInfo.getEmployeeDto())) {
//                continue;
//            }
//            //匹配
//            employeeMapping.setFsAccount("E." + fsEa + "." + fsEmployeeInfo.getEmployeeDto().getEmployeeId());
//            employeeMapping.setOutAccount(userId);
//            employeeMapping.setIsvAccount(userId);
//            employeeMapping.setOutEa(corpId);
//            employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
//            employeeMappingList.add(employeeMapping);
//        }
//        log.info("CorpManager.autoBindAccountEnterprise,employeeMappingList={}.", employeeMappingList);
//        //入库
//        if(CollectionUtils.isNotEmpty(employeeMappingList)) {
//            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
//        }
//    }

    public void autoBindAccountEnterprise2(String fsEa, String corpId, List<QyweixinUserDetailInfoRsp> qyweixinUserInfos) {
        log.info("CorpManager.autoBindAccountEnterprise2,fsEa={},corpId={},qyweixinUserInfos={}.", fsEa, corpId, qyweixinUserInfos);
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
        //过滤已绑定的员工
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, corpId);
        log.info("CorpManager.autoBindAccountEnterprise2,qyweixinAccountEmployeeMappings={}.", qyweixinAccountEmployeeMappings);
        Set<String> outAccountsWithBind = qyweixinAccountEmployeeMappings.stream()
                .map(QyweixinAccountEmployeeMapping::getIsvAccount)
                .collect(Collectors.toSet());
        qyweixinUserInfos = qyweixinUserInfos.stream()
                .filter(v -> !outAccountsWithBind.contains(v.getUserid()))
                .collect(Collectors.toList());
        log.info("CorpManager.autoBindAccountEnterprise2,qyweixinUserInfos={}.", qyweixinUserInfos);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(qyweixinUserInfos)) {
            return;
        }
        List<QyweixinAccountEmployeeMapping> employeeMappingList = new LinkedList<>();
        for(QyweixinUserDetailInfoRsp qyweixinUserInfo : qyweixinUserInfos) {
            QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
            //通过名称获取纷享CRM的员工的详情
            int fsId = 0;
            FindEmployeeDtoByFullNameResult fsEmployeeInfoByFullName = fsManager.getEmployeesByFullName(fsEa, qyweixinUserInfo.getName());
            if(ObjectUtils.isNotEmpty(fsEmployeeInfoByFullName) && CollectionUtils.isNotEmpty(fsEmployeeInfoByFullName.getEmployeeDtos())) {
                fsId = fsEmployeeInfoByFullName.getEmployeeDtos().get(0).getEmployeeId();
            } else {
                GetEmployeesDtoByNameResult fsEmployeeInfoByName = fsManager.getEmployeesByName(fsEa, qyweixinUserInfo.getName());
                if(ObjectUtils.isNotEmpty(fsEmployeeInfoByName) && ObjectUtils.isNotEmpty(fsEmployeeInfoByName.getEmployeeDto())) {
                    fsId = fsEmployeeInfoByName.getEmployeeDto().getEmployeeId();
                }
            }
            log.info("CorpManager.autoBindAccountEnterprise2,ea={},qyweixinUserInfo={},fsId={}.", fsEa, qyweixinUserInfo, fsId);
            if(fsId == 0) {
                continue;
            }
            String employeeId = "E." + fsEa + "." + fsId;
            //企业微信可以有重名的用户，虽然自动绑定的前提是不允许重名，但是有可能添加了重名的用户，这里还是得做一次判断
            Set<String> fsAccountsWithBind = qyweixinAccountEmployeeMappings.stream()
                    .map(QyweixinAccountEmployeeMapping::getFsAccount)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindAccountEnterprise2,fsAccountsWithBind={}.", fsAccountsWithBind);
            if(fsAccountsWithBind.contains(employeeId)) {
                continue;
            }
            Set<String> fsAccountsWithNotBind = employeeMappingList.stream()
                    .map(QyweixinAccountEmployeeMapping::getFsAccount)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindAccountEnterprise2,fsAccountsWithNotBind={}.", fsAccountsWithNotBind);
            if(fsAccountsWithNotBind.contains(employeeId)) {
                continue;
            }
            //匹配
            employeeMapping.setFsAccount(employeeId);
            employeeMapping.setOutAccount(qyweixinUserInfo.getUserid());
            employeeMapping.setIsvAccount(qyweixinUserInfo.getUserid());
            employeeMapping.setOutEa(corpId);
            employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
            employeeMappingList.add(employeeMapping);
        }
        log.info("CorpManager.autoBindAccountEnterprise2,employeeMappingList={}.", employeeMappingList);
        //入库
        if(CollectionUtils.isNotEmpty(employeeMappingList)) {
            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
        }
    }

    public void autoBindAccountEnterprise3(String fsEa, String corpId, List<QyweixinUserDetailInfoRsp> qyweixinUserInfos, EmployeeDto employeeDto) {
        log.info("CorpManager.autoBindAccountEnterprise3,fsEa={},corpId={},qyweixinUserInfos={},employeeDto={}.", fsEa, corpId, qyweixinUserInfos, employeeDto);
        List<QyweixinAccountEmployeeMapping> employeeMappingList = new LinkedList<>();

        //先查询名称，再查询昵称
        List<QyweixinUserDetailInfoRsp> userInfos = null;
        if(StringUtils.isNotEmpty(employeeDto.getFullName())) {
            userInfos = qyweixinUserInfos.stream().filter(v -> StringUtils.isNotEmpty(v.getName()) && v.getName().equals(employeeDto.getFullName())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(userInfos) && StringUtils.isNotEmpty(employeeDto.getName())) {
            userInfos = qyweixinUserInfos.stream().filter(v -> StringUtils.isNotEmpty(v.getName()) && v.getName().equals(employeeDto.getName())).collect(Collectors.toList());
        }
        log.info("CorpManager.autoBindAccountEnterprise3,fsEa={},userInfos={}.", fsEa, userInfos);
        if(CollectionUtils.isEmpty(userInfos)) {
            return;
        }
        QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
        //匹配
        employeeMapping.setFsAccount("E." + fsEa + "." + employeeDto.getEmployeeId());
        employeeMapping.setOutAccount(userInfos.get(0).getUserid());
        employeeMapping.setIsvAccount(userInfos.get(0).getUserid());
        employeeMapping.setOutEa(corpId);
        employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
        employeeMappingList.add(employeeMapping);
        log.info("CorpManager.autoBindAccountEnterprise3,employeeMappingList={}.", employeeMappingList);
        //入库
        if(CollectionUtils.isNotEmpty(employeeMappingList)) {
            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
        }
    }

    public void autoBindEmpAccount(String fsEa, String corpId, List<QyweixinUserDetailInfoRsp> qyweixinUserInfos) {
        log.info("CorpManager.autoBindEmpAccount,fsEa={},corpId={},qyweixinUserInfos={}.", fsEa, corpId, qyweixinUserInfos);
        String mainAppId = qyweixinGatewayInnerService.getMainAppId(corpId).getData();
        //过滤已绑定的员工
        List<QyweixinAccountEmployeeMapping> qyweixinAccountEmployeeMappings =
                qyweixinAccountBindInnerService.queryAccountBindByFsEa2(fsEa, mainAppId, corpId);
        log.info("CorpManager.autoBindEmpAccount,qyweixinAccountEmployeeMappings={}.", qyweixinAccountEmployeeMappings);
        Set<String> outAccountsWithBind = qyweixinAccountEmployeeMappings.stream()
                .map(QyweixinAccountEmployeeMapping::getIsvAccount)
                .collect(Collectors.toSet());
        qyweixinUserInfos = qyweixinUserInfos.stream()
                .filter(v -> !outAccountsWithBind.contains(v.getUserid()))
                .collect(Collectors.toList());
        log.info("CorpManager.autoBindEmpAccount,qyweixinUserInfos={}.", qyweixinUserInfos);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(qyweixinUserInfos)) {
            return;
        }
        Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        List<QyweixinAccountEmployeeMapping> employeeMappingList = new LinkedList<>();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for(QyweixinUserDetailInfoRsp qyweixinUserInfo : qyweixinUserInfos) {
            QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
            String fsId = null;
            String attrsText = null;
            //先看看是否有自定义字段
            if(ObjectUtils.isNotEmpty(qyweixinUserInfo.getExtattr()) && CollectionUtils.isNotEmpty(qyweixinUserInfo.getExtattr().getAttrs())) {
                for(QyweixinUserDetailInfoRsp.AttrsInfo attrsInfo : qyweixinUserInfo.getExtattr().getAttrs()) {
                    if(attrsInfo.getType() == 0 && attrsInfo.getName().equals(autoBindEmpEnterpriseMap.get(fsEa).get("QYWX_EXTATTR"))) {
                        attrsText = attrsInfo.getText().getValue();
                        break;
                    }
                }
            }
            if(StringUtils.isEmpty(attrsText)) {
                continue;
            }
            //根据自定义字段查询crm对象
            String fsPersonnelObject = fsManager.queryFsObject(ei, autoBindEmpEnterpriseMap.get(fsEa).get("FS_API_NAME"), attrsText, "PersonnelObj");
            if(StringUtils.isNotEmpty(fsPersonnelObject)) {
                JSONArray read = (JSONArray) JSONPath.read(fsPersonnelObject, "$.data.dataList");
                if(read.size()!=0){
                    fsId = JSONPath.read(fsPersonnelObject, "$.data.dataList[0].user_id").toString();
                }
            }
            log.info("CorpManager.autoBindEmpAccount,ea={},qyweixinUserInfo={},fsId={}.", fsEa, qyweixinUserInfo, fsId);
            if(StringUtils.isEmpty(fsId)) {
                continue;
            }
            String employeeId = "E." + fsEa + "." + fsId;
            //企业微信可以有重名的用户，虽然自动绑定的前提是不允许重名，但是有可能添加了重名的用户，这里还是得做一次判断
            Set<String> fsAccountsWithBind = qyweixinAccountEmployeeMappings.stream()
                    .map(QyweixinAccountEmployeeMapping::getFsAccount)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindEmpAccount,fsAccountsWithBind={}.", fsAccountsWithBind);
            if(fsAccountsWithBind.contains(employeeId)) {
                continue;
            }
            Set<String> fsAccountsWithNotBind = employeeMappingList.stream()
                    .map(QyweixinAccountEmployeeMapping::getFsAccount)
                    .collect(Collectors.toSet());
            log.info("CorpManager.autoBindEmpAccount,fsAccountsWithNotBind={}.", fsAccountsWithNotBind);
            if(fsAccountsWithNotBind.contains(employeeId)) {
                continue;
            }
            //匹配
            employeeMapping.setFsAccount(employeeId);
            employeeMapping.setOutAccount(qyweixinUserInfo.getUserid());
            employeeMapping.setIsvAccount(qyweixinUserInfo.getUserid());
            employeeMapping.setOutEa(corpId);
            employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
            employeeMappingList.add(employeeMapping);
        }
        log.info("CorpManager.autoBindEmpAccount,employeeMappingList={}.", employeeMappingList);
        //入库
        if(CollectionUtils.isNotEmpty(employeeMappingList)) {
            qyweixinAccountBindService.bindAccountEmployeeMapping(employeeMappingList);
        }
    }

    public void enterpriseBind(String corpId,String fsEa,int status) {
        // 绑定新建企业
        QyweixinAccountEnterpriseMapping arg = new QyweixinAccountEnterpriseMapping();
        arg.setSource(SourceTypeEnum.QYWX.getSourceType());
        arg.setOutEa(corpId);
        arg.setFsEa(fsEa);
        arg.setStatus(status);
        arg.setDomain(ConfigCenter.crm_domain);
        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountEnterpriseMapping(arg);
        LogUtils.info("CorpManager.enterpriseBind,bindAccountEnterpriseMapping,result={}", result);
    }

    public void updateEnterpriseBindStatus(String fsEa,int status) {
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateEnterpriseBindStatus(fsEa, status);
        LogUtils.info("CorpManager.updateEnterpriseBindStatus,result={}", result);
    }

    public void employeeBind(String corpId,String fsEa,String appId,String outUserId,int status) {
        QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
        employeeMapping.setSource(SourceTypeEnum.QYWX.getSourceType());
        employeeMapping.setOutEa(corpId);
        employeeMapping.setAppId(appId);
        employeeMapping.setFsAccount("E." + fsEa + "." + ConfigCenter.FIRST_EMPLOYEE_ID);
        employeeMapping.setOutAccount(outUserId);
        employeeMapping.setIsvAccount(outUserId);
        employeeMapping.setStatus(status);
        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountEmployeeMapping(Lists.newArrayList(employeeMapping));
        LogUtils.info("CorpManager.employeeBind,bindAccountEmployeeMapping,result={}", result);
    }

    public void updateEmployeeBindStatus(String fsAccount,int status) {
        com.facishare.open.qywx.accountbind.result.Result<Integer> result = qyweixinAccountBindService.updateEmployeeBindStatus(fsAccount, status);
        LogUtils.info("CorpManager.updateEmployeeBindStatus,result={}", result);
    }

    /**
     * 代开发应用是否安装
     * @param outEa
     * @return
     */
    public boolean isRepAppInstalled(String outEa) {
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(outEa, repAppId);
        if(corpBindBo!=null && corpBindBo.getStatus()==0) return true;
        return false;
    }
}
