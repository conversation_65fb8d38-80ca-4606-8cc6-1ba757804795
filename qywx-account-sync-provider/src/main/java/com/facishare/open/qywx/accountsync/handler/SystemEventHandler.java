package com.facishare.open.qywx.accountsync.handler;

import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.manager.QywxPermissionManager;
import com.facishare.open.qywx.accountsync.model.qyweixin.QywxPermissionOrderDetail;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.accountsync.utils.xml.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业微信代开发应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class SystemEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private QywxPermissionManager qywxPermissionManager;

    @Override
    public void handle(String plainMsg) {
        super.handle(plainMsg);

        //String plainMsg = null;
        try {
//            plainMsg = decryptMsg(eventProto.getSignature(), eventProto.getTimestamp(),
//                    eventProto.getNonce(), eventProto.getData(),eventProto.getAppId());
            log.info("RepEventHandler.handle,plainMsg={}", plainMsg);

            //QyweixinMsgBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QyweixinMsgBaseXml.class);
            QyweixinPaySuccessMsgBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QyweixinPaySuccessMsgBaseXml.class);
            log.info("RepEventHandler.handle,baseMsgXml={}",baseMsgXml);
            String infoType = baseMsgXml.getInfoType();
            log.info("RepEventHandler.handle,infoType={}",infoType);

            if(StringUtils.isEmpty(infoType)) {
                EventXml eventXml = XStreamUtils.parseXml(plainMsg, EventXml.class);
                infoType = eventXml.getEvent();
                log.info("RepEventHandler.handle,infoType2={}",infoType);
            }
            String outEa = baseMsgXml.getAuthCorpId();
            log.info("RepEventHandler.repMsgEvent,outEa={}", outEa);
            //判断当前企业数据是否可以在当前环境运行
            if(runInCurrentEnv(outEa,plainMsg)==false){
                return;
            }
            if(infoType.equals(Constant.PAY_LICENSE_SUCCESS)){

                // 根据订单查询账号信息
                qywxPermissionManager.handlerOrderPaySuccess(baseMsgXml.getOrderId());
            }
            if(infoType.equals(Constant.AUTO_ACTIVE)){
                // 自动激活
                QyweixinAutoActiveMsgBaseXml qyweixinAutoActiveMsgBaseXml=XStreamUtils.parseXml(plainMsg,QyweixinAutoActiveMsgBaseXml.class);
                List<String> activeCodes = qyweixinAutoActiveMsgBaseXml.getAccountList().stream().map(QyweixinAutoActiveMsgBaseXml.Account::getActiveCode).collect(Collectors.toList());
                qywxPermissionManager.handlerAutoActive(activeCodes,qyweixinAutoActiveMsgBaseXml.getAuthCorpId());
            }


        } catch (Exception e) {
            log.error("RepEventHandler.handle,exception={}", e.getMessage(),e);
            return;
        }

    }
}
