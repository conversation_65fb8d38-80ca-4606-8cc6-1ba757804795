package com.facishare.open.qywx.accountsync.handler;

import com.facishare.open.common.utils.AesException;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppConfigInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.utils.DateUtils;
import com.facishare.open.qywx.accountsync.utils.QYWxCryptHelper;
import com.facishare.open.qywx.accountsync.utils.SecurityUtil;
import com.facishare.open.qywx.accountsync.utils.xml.EncryptXml;
import com.facishare.open.qywx.accountsync.utils.xml.TagChangeEventXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
public abstract class EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinCorpBindDao qyweixinCorpBindDao;
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;

    @ReloadableProperty("gray.outEa.bind.time.greater")
    private String grayOutEaBindTimeGreater;

    @ReloadableProperty("gray.outEa.list")
    private String grayOutEaList;

    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    protected Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();

    protected List<String> supportedCmdEvent = Lists.newArrayList(
            "suite_ticket",
            "create_auth","change_auth","cancel_auth","change_contact",
            "open_order","change_order","pay_for_app_success","refund",
            "reset_permanent_code","agree_external_userid_migration",
            "change_external_contact","change_external_chat","enter_agent",
            "share_chain_change"
    );

    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("EnterpriseWeChatEventHandler.initAppMeta, appMetaInfoStr={}, appMetaComponent={} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
    }

    public void handle(String plainMsg) {
    }

    public String decryptMsg(String msgSignature, String timeStamp, String nonce, String postData,String appId) throws AesException {
        log.info("EnterpriseWeChatEventHandler.decryptMsg,msgSignature={},timeStamp={},nonce={},postData={}",
                msgSignature,timeStamp,nonce,postData);
        log.info("EnterpriseWeChatEventHandler.decryptMsg,appMetaInfo={}", appMetaInfo);

        EncryptXml encryptXml = null;
        if(StringUtils.isEmpty(appId)) {
            //EncryptXml encryptXml = XmlParser.fromXml(postData, EncryptXml.class);
            encryptXml = XStreamUtils.parseXml(postData, EncryptXml.class);
            if(StringUtils.isEmpty(encryptXml.getAgentId())) {
                //agentId为空，则toUserName字段存放的是appId
                appId = encryptXml.getToUserName();
            } else {
                //agentId不为空，则toUserName字段存放的是corpId
                QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.findByAgentId(encryptXml.getToUserName(), encryptXml.getAgentId());
                log.info("EnterpriseWeChatEventHandler.decryptMsg,corpBindBo={}", corpBindBo);
                if(corpBindBo!=null) {
                    appId = corpBindBo.getAppId();
                } else {
                    //首次安装应用，可能找不到应用的绑定关系，因为应用的绑定关系可能还没有入库，默认为crm应用的ID
                    appId = ConfigCenter.crmAppId;
                    log.info("EnterpriseWeChatEventHandler.decryptMsg,appId={}",appId);
                }
            }
        }

        log.info("EnterpriseWeChatEventHandler.decryptMsg,appId={}", appId);
        String token = appMetaInfo.get(appId).getToken();
        byte[] aesKey = appMetaInfo.get(appId).getEncodingAESKeyBase64();
        return QYWxCryptHelper.DecryptMsg(token,aesKey, msgSignature, timeStamp, nonce, postData, encryptXml);
    }

    /**
     * 判断当前企业数据是否可以在当前环境运行
     * @param outEa
     * @param plainMsg
     * @return
     */
    protected boolean runInCurrentEnv(String outEa,String plainMsg) {
        log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,outEa={},plainMsg={}",outEa,plainMsg);
        //不是纷享云环境，只有一个
        if(!ConfigCenter.MAIN_ENV) {
            log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,MAIN_ENV={}", ConfigCenter.MAIN_ENV);
            return Boolean.TRUE;
        }

        //ea为空，有可能是代开发应用的事件，需要重新解析
        if (StringUtils.isEmpty(outEa)) {
            //tagChangeEventXml = XmlParser.fromXml(plainMsg, TagChangeEventXml.class);
            TagChangeEventXml tagChangeEventXml = XStreamUtils.parseXml(plainMsg, TagChangeEventXml.class);
            log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,tagChangeEventXml={}", tagChangeEventXml);
            if(StringUtils.isNotEmpty(tagChangeEventXml.getToUserName())) {
                outEa = tagChangeEventXml.getToUserName();
            }
        }
        boolean isGray = isGrayEnv();
        if(StringUtils.isNotEmpty(outEa)) {
            boolean runInGray = canRunInGrayEnv(outEa);
            log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,outEa={},runInGray={},isGrayEnv={}",outEa,runInGray,isGray);
            if(runInGray && !isGray) {
                log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,only run in gray env,plainMsg={}",plainMsg);
                return false;
            }
            if(isGray && runInGray==false) {
                log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,not run in gray env,plainMsg={}",plainMsg);
                return false;
            }
        } else {
            if(StringUtils.containsIgnoreCase(plainMsg,"<InfoType><![CDATA[create_auth]]></InfoType>")
                    || StringUtils.containsIgnoreCase(plainMsg,"<InfoType><![CDATA[pay_for_app_success]]></InfoType>")) {
                if(StringUtils.isEmpty(grayOutEaList) && StringUtils.isEmpty(grayOutEaBindTimeGreater)) {
                    if(isGray) {
                        return false;
                    } else {
                        log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,run in normal env,plainMsg={}",plainMsg);
                        return true;
                    }
                } else {
                    if(isGray) {
                        log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,run in gray env,plainMsg={}",plainMsg);
                        return true;
                    } else {
                        return false;
                    }
                }
            }
        }
        log.info("EnterpriseWeChatEventHandler.runInCurrentEnv,end,outEa={},plainMsg={}",outEa,plainMsg);
        return true;
    }

    private boolean isGrayEnv() {
        String env = System.getProperty("process.profile");
        log.info("EnterpriseWeChatEventHandler.isGrayEnv,env={}", env);
        if (StringUtils.equalsIgnoreCase(env, "fstest-gray") || StringUtils.equalsIgnoreCase(env, "foneshare-gray")) return true;
        return false;
    }

    /**
     * 判断当前企业是否可以走灰度环境
     * @param outEa
     * @return
     */
    private boolean canRunInGrayEnv(String outEa) {
        //1.先判断企业是否在灰度企业列表里面
        if (StringUtils.isNotEmpty(grayOutEaList) && StringUtils.containsIgnoreCase(grayOutEaList, outEa)) {
            log.info("EnterpriseWeChatEventHandler.canRunInGrayEnv,in grayOutEaList,outEa={}", outEa);
            return true;
        }

        //2.如果企业不在灰度企业列表，再判断企业创建日期是否大于指定的灰度日期
        log.info("EnterpriseWeChatEventHandler.canRunInGrayEnv,grayOutEaBindTimeGreater={}", grayOutEaBindTimeGreater);
        Date date = null;
        if (StringUtils.isNotEmpty(grayOutEaBindTimeGreater)) {
            date = DateUtils.parseDate(grayOutEaBindTimeGreater, "yyyy-MM-dd");
            if (date == null) return false;
            log.info("EnterpriseWeChatEventHandler.canRunInGrayEnv,query enterprise mapping,grayOutEaBindTimeGreater={}", date);

            Result<List<QyweixinAccountEnterpriseMapping>> result = qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(),
                    outEa);
            log.info("EnterpriseWeChatEventHandler.canRunInGrayEnv,query enterprise mapping,outEa={},result={}", outEa,result);
            if (CollectionUtils.isEmpty(result.getData())) return false;

            QyweixinAccountEnterpriseMapping enterpriseMapping = result.getData().get(0);
            if(enterpriseMapping.getGmtCreate().getTime() > date.getTime()) {
                log.info("EnterpriseWeChatEventHandler.canRunInGrayEnv,grayOutEaBindTimeGreater,outEa={}", outEa);
                return true;
            }
        }
        return false;
    }
}