package com.facishare.open.qywx.accountsync.handler;

import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.accountsync.utils.xml.QYWeiXinDataEventBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业微信第三方应用事件处理器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component
public class ThirdDataEventHandler extends EnterpriseWeChatEventHandler {
    @Resource
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;

    private static List<String> supportedDataEvent = Lists.newArrayList(
            "subscribe","unsubscribe"
    );

    @Override
    public void handle(String plainMsg) {
        super.handle(plainMsg);
        //log.info("ThirdDataEventHandler.handle,eventProto={}",eventProto);

        try {
//            String plainMsg = decryptMsg(eventProto.getSignature(), eventProto.getTimestamp(),
//                    eventProto.getNonce(), eventProto.getData(),eventProto.getAppId());
            log.info("ThirdDataEventHandler.handle,plainMsg={}",plainMsg);

            //QYWeiXinDataEventBaseXml baseMsgXml = XmlParser.fromXml(plainMsg, QYWeiXinDataEventBaseXml.class);
            QYWeiXinDataEventBaseXml baseMsgXml = XStreamUtils.parseXml(plainMsg, QYWeiXinDataEventBaseXml.class);
            log.info("ThirdDataEventHandler.handle,baseMsgXml={}",baseMsgXml);

            if(runInCurrentEnv(baseMsgXml.getToUserName(),plainMsg)==false) return;

            String event = baseMsgXml.getEvent();
            if(supportedDataEvent.contains(event)) {
                log.info("ThirdDataEventHandler.handle,plainMsg2={}", plainMsg);
                qyweixinGatewayInnerService.recvDataEvent(plainMsg, ConfigCenter.crmAppId);
            } else {
                log.info("ThirdDataEventHandler.handle,not supported event,event={}",event);
            }

        } catch (Exception e) {
            log.error("ThirdDataEventHandler.handle,DecryptMsg,exception={}",e.getMessage(),e);
        }
    }
}
