package com.facishare.open.qywx.accountsync.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.handler.RepEventHandler;
import com.facishare.open.qywx.accountsync.handler.SystemEventHandler;
import com.facishare.open.qywx.accountsync.handler.ThirdCmdEventHandler;
import com.facishare.open.qywx.accountsync.handler.ThirdDataEventHandler;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto2;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 企业微信MQ事件消息接收器
 * <AUTHOR>
 * @date 2022/12/20
 */
@Slf4j
@Component("enterpriseWeChatMQListener")
public class EnterpriseWeChatMQListener implements MessageListenerConcurrently {
    @Resource
    private ThirdCmdEventHandler thirdCmdEventHandler;
    @Resource
    private ThirdDataEventHandler thirdDataEventHandler;
    @Resource
    private RepEventHandler repEventHandler;
    @Autowired
    private SystemEventHandler systemEventHandler;

    private List<String> supportTagList = Lists.newArrayList(
            EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD,
            EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_DATA_EVENT_4_THIRD,
            EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP,
            EnterpriseWeChatEventTag.TAG_SYSTEM_EVENT_NOTIFY
            );

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt item : msgs) {
            try {
                TraceUtils.initTraceId(item.getMsgId());
                log.info("EnterpriseWeChatMQConsumer.consumeMessage,msgId={},item={}", item.getMsgId(), item);
                if(!supportTagList.contains(item.getTags())) continue;
                EnterpriseWeChatEventProto2 eventProto = new EnterpriseWeChatEventProto2();
                eventProto.fromProto(item.getBody());
                log.info("EnterpriseWeChatMQConsumer.consumeMessage,eventProto={}", JSONObject.toJSONString(eventProto));

                if(StringUtils.equalsIgnoreCase(item.getTags(), EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD)) {
                    thirdCmdEventHandler.handle(eventProto.getPlainMsg());
                }
                else if(StringUtils.equalsIgnoreCase(item.getTags(), EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_DATA_EVENT_4_THIRD)) {
                    thirdDataEventHandler.handle(eventProto.getPlainMsg());
                }
                else if(StringUtils.equalsIgnoreCase(item.getTags(),EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_EVENT_4_REP)) {
                    repEventHandler.handle(eventProto.getPlainMsg());
                }  else if(StringUtils.equalsIgnoreCase(item.getTags(),EnterpriseWeChatEventTag.TAG_SYSTEM_EVENT_NOTIFY)) {
                    systemEventHandler.handle(eventProto.getPlainMsg());
                }

                else {
                    log.info("EnterpriseWeChatMQConsumer.consumeMessage,not supported tags={}", item.getTags());
                }
            } catch (Exception e) {
                log.info("EnterpriseWeChatMQConsumer.consumeMessage,error,,msgId={},item={}", item.getMsgId(), item);
                log.error("EnterpriseWeChatMQConsumer.consumeMessage,exception", e);
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
