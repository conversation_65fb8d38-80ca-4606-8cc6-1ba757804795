package com.facishare.open.qywx.accountsync.mq;

import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountsync.config.ConfigCenter;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;


/**
 * 1、目前只用于账号同步绑定
 * <AUTHOR>
 * @Version 1.0
 */
@Component
@Slf4j
public class UserRoleEventListener extends OrganizationChangedListener {

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    ContactBindInnerService contactBindInnerService;

    public UserRoleEventListener() {
        super("fs-open-qywx-app-config");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        String traceId = TraceUtils.getTraceId();
        if(StringUtils.isEmpty(traceId)) {
            TraceUtils.initTraceId(UUID.randomUUID()+"_"+event.getEnterpriseAccount());
        }
        log.info("UserRoleEventListener.onEmployeeChanged.event={}.", event);
        String fsEa = event.getEnterpriseAccount();
        if(ObjectUtils.isNotEmpty(event.getOldEmployeeDto())) {
            //不是新建人员过滤
            return;
        }
        log.info("UserRoleEventListener.onEmployeeChanged.fsEa={},AUTO_BIND_ACCOUNT_EA={}.", fsEa, ConfigCenter.AUTO_BIND_ACCOUNT_EA);
        if(!ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
            //不是灰度名单
            return;
        }

        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaResult("qywx", fsEa);
        log.info("UserRoleEventListener.onEmployeeChanged.qyweixinCorpIDResult={}.", qyweixinCorpIDResult);
        if (!qyweixinCorpIDResult.isSuccess() || ObjectUtils.isEmpty(qyweixinCorpIDResult.getData())) {
            return;
        }
        contactBindInnerService.listenMqEventToBindAccount(fsEa, qyweixinCorpIDResult.getData().getIsvOutEa(), event.getNewEmployeeDto());
    }
}
