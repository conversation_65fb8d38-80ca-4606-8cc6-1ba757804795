package com.facishare.open.qywx.accountsync.mq.listener;

import com.alibaba.fastjson.JSONObject;
import com.facishare.enterprise.event.EnterpriseAddEvent;
import com.facishare.enterprise.event.EnterpriseEventType;
import com.facishare.open.order.contacts.proxy.api.service.FsEventService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.api.utils.ProtoUtil;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 纷享企业事件接收器，主要处理企业创建事件
 * <AUTHOR>
 * @date 2023/02/06
 */
@Slf4j
@Component("enterpriseEventListener")
public class EnterpriseEventListener implements MessageListenerConcurrently {
    @Resource
    private FsEventService fsEventService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            TraceUtils.initTraceId(msg.getMsgId());
            LogUtils.info("EnterpriseEventListener.consumeMessage,msg={}",msg);
            try {
                if(msg.getFlag()== EnterpriseEventType.EnterpriseAdd.getType()){
                    LogUtils.info("EnterpriseEventListener.consumeMessage,body={},properties={}",
                            JSONObject.toJSONString(msg.getBody()),
                            JSONObject.toJSONString(msg.getProperties()));

                    EnterpriseAddEvent enterpriseAddEvent = ProtoUtil.fromProto(msg.getBody(), EnterpriseAddEvent.class);
                    LogUtils.info("EnterpriseEventListener.consumeMessage,enterpriseAddEvent={}",enterpriseAddEvent);

                    if(fsEventService.isEnterpriseBind(enterpriseAddEvent.getEnterpriseAccount()).getData()) {
                        fsEventService.onEnterpriseOpened(enterpriseAddEvent.getEnterpriseId(),
                                enterpriseAddEvent.getEnterpriseAccount(),
                                enterpriseAddEvent.getEnterpriseName());
                    } else {
                        LogUtils.info("EnterpriseEventListener.consumeMessage,enterprise not bind,enterpriseAddEvent={}",enterpriseAddEvent);
                    }
                }
            } catch (Exception e) {
                LogUtils.info("EnterpriseEventListener.consumeMessage,exception={}",e.getMessage(),e);
                //不能直接返回，因为这是批量消费
//                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
