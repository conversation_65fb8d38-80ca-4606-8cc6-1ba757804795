package com.facishare.open.qywx.accountsync.mq;

import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.accountsync.model.CreateCrmEnterpriseEventProto;
import com.facishare.open.qywx.accountsync.model.EnterpriseWeChatEventProto2;
import com.facishare.open.qywx.accountinner.model.OutEventDateChangeProto;
import com.facishare.open.qywx.accountsync.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MQ发送专用工具
 * <AUTHOR>
 * @date ********
 */
@Slf4j
@Component
public class MQSender {
    @Resource(name = "enterpriseWechatEventMQSender")
    private AutoConfRocketMQProducer enterpriseWechatEventMQSender;

    @Resource(name = "outEventDataChangeMQSender")
    private AutoConfRocketMQProducer outEventDataChangeMQSender;

    /**
     * 发送企业微信解密后的事件MQ
     * @param tag 事件类型tag
     * @param plainMsg 明文企微事件消息
     */
    public void sendEnterpriseWeChatMQ(String tag,String plainMsg) {
        log.info("MQSender.sendEnterpriseWeChatMQ,plainMsg={}", plainMsg);

        EnterpriseWeChatEventProto2 eventProto2 = new EnterpriseWeChatEventProto2();
        eventProto2.setPlainMsg(plainMsg);

        Message msg = new Message();
        msg.setTags(tag);
        msg.setBody(eventProto2.toProto());
        SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
        log.info("MQSender.sendEnterpriseWeChatMQ,sendResult={},plainMsg={}", sendResult, plainMsg);
    }

    public void sendEnterpriseCreateMQ(String tag, CreateCrmEnterpriseEventProto eventProto) {
        Message msg = new Message();
        msg.setTags(tag);
        msg.setBody(eventProto.toProto());
        SendResult sendResult = enterpriseWechatEventMQSender.send(msg);
        log.info("MQSender.sendEnterpriseCreateMQ,sendResult={},eventProto={}", sendResult, eventProto);
    }

    public void sendCloudProxyMQ(Integer ei, CloudMessageProxyProto proto) {
        Message msg = new Message();
        msg.setTags("cloud_proxy_event_tag");
        msg.setBody(proto.toProto());

        //把纷享云的MQ投递到所有的专属云
        TraceContext context = TraceContext.get();
        context.setEi(String.valueOf(ei));

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("MQSender.sendCloudProxyMQ,sendResult={}",sendResult);
    }

    public SendResult sendOutEventDataChangeMQ(String tag, OutEventDateChangeProto proto, String ei) {
        Message msg = new Message();
        msg.setTopic(outEventDataChangeMQSender.getDefaultTopic());
        msg.setTags(tag);
        msg.setBody(proto.toProto());

        TraceContext context = TraceContext.get();
        context.setEi(ei);  // 可以区分是否跨云投递
        context.setTraceId(TraceUtil.get());  // 日记记录，分布式跟踪需要

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("MQSender.sendOutEventDataChangeMQ.ei={},tag={},proto={},sendResult={}", ei, tag, proto, sendResult);
        return sendResult;
    }
}
