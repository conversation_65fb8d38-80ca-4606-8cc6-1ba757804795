package com.facishare.open.qywx.accountsync.test;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountsync.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinExternalContactDao;
import com.facishare.open.qywx.accountsync.manager.CorpManager;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.model.TagDetailModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactBo;
import com.facishare.open.qywx.accountsync.result.UploadResult;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.xml.SuiteAuthXml;
import com.fxiaoke.common.Pair;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QYWeixinManagerTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;
    @Autowired
    private QyweixinCorpBindDao qyweixinCorpBindDao;
    @Autowired
    private QyweixinExternalContactDao qyweixinExternalContactDao;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @ReloadableProperty("repAppId")
    private String repAppId;


    @Test
    public void getPermanentCode() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetPermenantCodeRsp> rsp = qyWeixinManager.getPermanentCode(
                "J63BQzGSD-6mvnucbk-BO5RY_JYulCB_BpwfNJBgCMBLEzZd6BJNSYw9j71S_0RLDK-oMZ7oXu8JOUiz8eSG0oCs_PXoUp8ES2DUV-Ecjf8",
                "dk97b554f2a62c74ff");
        System.out.println(rsp);
    }

    @Test
    public void getAdminList() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAdminListRsp> rsp = qyWeixinManager.getAdminList("wpwx1mDAAApDuHDla66rUas9XUWu0rdg",
                crmAppId,
                1000006);
        System.out.println(rsp);
    }

    @Test
    public void getJsApiTicket()  {
        com.facishare.open.qywx.accountinner.result.Result<String> jsApiTicket = qyWeixinManager.getJsApiTicket("wx88a141937dd6f838", "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g", true);
        System.out.println(jsApiTicket);
    }

    @Test
    public void getAgentJsApiTicket()  {
        com.facishare.open.qywx.accountinner.result.Result<String> jsApiTicket = qyWeixinManager.getAgentJsApiTicket("wx88a141937dd6f838", "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g", true);
        System.out.println(jsApiTicket);
    }

    @Test
    public void getQyweixinExternalContact()  {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactInfo> qyweixinExternalContact = qyWeixinManager.getQyweixinExternalContact("dkdf3684b6720635f7",
                "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",
                "wmwx1mDAAA8k3XhgX9rJXxBzhYwWM9Jw",
                "wowx1mDAAAqIYuayBsSlRjaZgAiAaPTQ");
        System.out.println(qyweixinExternalContact);
    }

    @Test
    public void getAppAccessToken() {
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7");
        com.facishare.open.qywx.accountinner.result.Result<String> appAccessToken = qyWeixinManager.getToken(qyweixinCorpBindBo.getPermanentCode(), "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        com.facishare.open.qywx.accountinner.result.Result<String> externalUserId = qyWeixinManager.toServiceExternalUserId(appAccessToken.getData(), "wmQZ1uJQAAKLIFwOryBbQ54eTC4vwkpg");
        System.out.println(appAccessToken);
        System.out.println(externalUserId);
    }

    @Test
    public void getAppAccessToken2() {
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind("ww7611fdf9bbbd3c67", "dkdf3684b6720635f7");
        com.facishare.open.qywx.accountinner.result.Result<String> appAccessToken = qyWeixinManager.getToken(qyweixinCorpBindBo.getPermanentCode(),"ww7611fdf9bbbd3c67");
        QyweixinExternalContactBo contactBo = QyweixinExternalContactBo.builder()
                .outEa("ww7611fdf9bbbd3c67")
                .outUserId("wowx1mDAAAIlE9l25FtHdbyv2kedv9Pg")
                .isvExternalUserId("wmwx1mDAAAI4_cBIlg_bZFUNSuEjOT4A")
                .build();
        List<QyweixinExternalContactBo> entityList = qyweixinExternalContactDao.findByEntity(contactBo);
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactRsp> externalContactRsp = qyWeixinManager.getExternalContactDetail(qyweixinCorpBindBo.getPermanentCode(),
                "ww7611fdf9bbbd3c67", entityList.get(0).getExternalUserId(),"wowx1mDAAAIlE9l25FtHdbyv2kedv9Pg");
        System.out.println(entityList);
    }

    @Test
    public void getExternalContactDetail() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactRsp> externalContactRsp = qyWeixinManager.getExternalContactDetail("ji1ttzMg7vlVwYWBPJqPtMxRnK0W1vbMZ2l1CMP3c7k",
                "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",
                "wmwx1mDAAAZCJZ1Q6RTdses7Ml0hpKHA",
                "wowx1mDAAAIlE9l25FtHdbyv2kedv9Pg");
        System.out.println(externalContactRsp);
    }

    @Test
    public void getContactList() {
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7");
        com.facishare.open.qywx.accountinner.result.Result<String> appAccessToken =qyWeixinManager.getToken(qyweixinCorpBindBo.getPermanentCode(),"wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinExternalContactRsp>> contactListResult = qyWeixinManager.getContactList(qyweixinCorpBindBo.getPermanentCode(),
                "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", null, "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        for(QyweixinExternalContactRsp rsp : contactListResult.getData()) {
            if(rsp.getErrcode()!=0) continue;
            QyweixinExternalContactBo contactBo = QyweixinExternalContactBo.builder()
                    .outEa("ww7611fdf9bbbd3c67")
                    .outUserId("wubeibei")
                    .externalUserId(rsp.getExternal_contact().getExternal_userid())
                    .build();
            List<QyweixinExternalContactBo> entityList = qyweixinExternalContactDao.findByEntity(contactBo);
            contactBo.setExternalName(rsp.getExternal_contact().getName());
            contactBo.setAvatar(rsp.getExternal_contact().getAvatar());
            if(CollectionUtils.isEmpty(entityList)) {
                int insert = qyweixinExternalContactDao.insert(contactBo);
                System.out.println(insert);
            }
            com.facishare.open.qywx.accountinner.result.Result<String> externalUserId = qyWeixinManager.toServiceExternalUserId(appAccessToken.getData(), contactBo.getExternalUserId());
            contactBo.setIsvExternalUserId(externalUserId.getData());
            int update = qyweixinExternalContactDao.updateExternalNameAndAvatar(contactBo.getOutEa(),
                    contactBo.getOutUserId(),
                    contactBo.getExternalUserId(),
                    contactBo.getExternalName(),
                    contactBo.getAvatar());
            System.out.println(update);
        }
        System.out.println(contactListResult);
    }

    @Test
    public void getQyweixinExternalContact2() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinExternalContactInfo> qyweixinExternalContact2 = qyWeixinManager.getQyweixinExternalContact2("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",null,
                "wmMRLhDAAATqzOxklvndauqA5oClexWQ","wowx1mDAAAIlE9l25FtHdbyv2kedv9Pg");
        System.out.println(qyweixinExternalContact2);
    }

    @Test
    public void test222() {
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=F6FK8J6vxz9gq3wQqKkFF9o4QCUAHsI8T_RG7T4RGC_NG41fd0LjM0XvDALDqGXLxOEf-w3Mv6TOcdz66tuvhC6mgUCz9_5uCZDcsORbMIajFrLiJR047DVDjTZ2N61jjgBCciGQNVHyCg_99llxAqRahGOZACtSIlbCnBndxH6o6lYK59ApJLmIGb9LLq6org6deQPoaayzUHUO-ZKzmg";
        Map<String, Object> argMaps = Maps.newHashMap();
        argMaps.put("userid", "wowx1mDAAADFFAttVhGETHzFGZ73IbQw");
        argMaps.put("cursor", null);
        argMaps.put("limit", 100);
        HttpHelper httpHelper = new HttpHelper();
        try {
            String result = httpHelper.postObjectData(getContact, argMaps);
            System.out.println(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getAppLicenseInfo() {
        com.facishare.open.qywx.accountinner.result.Result<AppLicenseInfo> appLicenseInfo = qyWeixinManager.getAppLicenseInfo("ww09d22666372d0efa","wxdeb7e0658a828754");
        String json = JSONObject.toJSONString(appLicenseInfo.getData());
        System.out.println(appLicenseInfo);
    }

    @Test
    public void getAppInfo() {
        //dkdf3684b6720635f7
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfo = qyWeixinManager.getAppInfo("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wx88a141937dd6f838");
        System.out.println(appInfo);
    }

    @Test
    public void getUserListInAppVisibleRange() {
        com.facishare.open.qywx.accountinner.result.Result<List<String>> list = qyWeixinManager.getUserListInAppVisibleRange("ww7611fdf9bbbd3c67", "wx88a141937dd6f838");
        System.out.println(list);
    }

    @Test
    public void corpId2OpenCorpId() {
        String corpId = qyWeixinManager.corpId2OpenCorpId("ww0cf1f8b575ddcc0f").getData();
        System.out.println(corpId);
    }

    @Test
    public void userId2OpenUserId() {
        com.facishare.open.qywx.accountinner.result.Result<List<QyweixinOpenUserIdInfo>> result = qyWeixinManager.userId2OpenUserId(Lists.newArrayList("WuBeiBei"),
                "wpwx1mDAAAJexomNGFhECY68trViPz3A",
                crmAppId);
        System.out.println(result);
    }

    @Test
    public void outAccountToFsAccount() {
        Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindService.outAccountToFsAccount("qywx",
                "84801", "", "beibei");
        System.out.println(result);
    }

    @Test
    public void uploadFile() {
        File file = new File("D:\\test\\企业应用代开发授权二维码.pdf");
        com.facishare.open.qywx.accountinner.result.Result<UploadResult> result = qyWeixinManager.uploadFile("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",file, "file");
        System.out.println(result);
    }


    public static void main(String[] args) {
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=F6FK8J6vxz9gq3wQqKkFF9o4QCUAHsI8T_RG7T4RGC_NG41fd0LjM0XvDALDqGXLxOEf-w3Mv6TOcdz66tuvhC6mgUCz9_5uCZDcsORbMIajFrLiJR047DVDjTZ2N61jjgBCciGQNVHyCg_99llxAqRahGOZACtSIlbCnBndxH6o6lYK59ApJLmIGb9LLq6org6deQPoaayzUHUO-ZKzmg";
        Map<String, Object> argMaps = Maps.newHashMap();
        argMaps.put("userid", "wowx1mDAAADFFAttVhGETHzFGZ73IbQw");
        argMaps.put("cursor", null);
        argMaps.put("limit", 100);
        HttpHelper httpHelper = new HttpHelper();
        try {
            String result = httpHelper.postObjectData(getContact, argMaps);
            int errorCode = (int) JSONPath.read(result, "$.errcode");
            System.out.println(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetUserInfo() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfo = qyWeixinManager.getUserInfo("dk34d6bacd61e33d99",
                "wpwx1mDAAAsJuqgPFlcfGWtLOewfEiNw",
                "wowx1mDAAA0Ou5xip4toV7W4cvRmepcw");
        System.out.println(userInfo);
    }

    @Test
    public void testGetUserInfoFromSelf() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> rsp1 = qyWeixinManager.getUserInfoFromSelf("BOa_EUyTPlum8hZMBAWOpINY1L0BofWnHRLWSDD4UIo",
                "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA",
                "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        System.out.println(rsp1);
    }

    @Test
    public void testGetTagEmployeeList() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinTagEmployeeListRsp> rsp1 = qyWeixinManager.getTagEmployeeList("dkdf3684b6720635f7",
                "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA",
                "********");
        System.out.println(rsp1);
    }

    @Test
    public void findByAgentId() {
        QyweixinCorpBindBo corpBindBo = qyweixinCorpBindDao.findByAgentId("wpwx1mDAAAuG1v2t0E7AmVx4Gyh9WO_w",
                "1000002");
        System.out.println(corpBindBo);
    }

    @Test
    public void getCorpAccessTokenTest() {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getCorpAccessToken(crmAppId, "wpwx1mDAAAkhYZe_RrlHI4ZBuVk1zX0Q", null);
        System.out.println(result);
    }

    @Test
    public void getCorpAccessTokenForCheckTest() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetCorptokenRsp> result = qyWeixinManager.getCorpAccessTokenForCheck(crmAppId, "wpwx1mDAAAkhYZe_RrlHI4ZBuVk1zX0Q");
        System.out.println(result);
        if (!result.isSuccess() && result.getCode().equals("40084")) {
            System.out.println(true);
        } else {
            System.out.println(false);
        }
    }

    @Test
    public void getProviderAccessTokenTest() {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getProviderAccessToken(null);
        System.out.println(result);
    }

    @Test
    public void doGetUserPhoneTest() {
        com.facishare.open.qywx.accountinner.result.Result<Pair<String, String>> result = qyWeixinManager.getUserPhone(crmAppId, "wpwx1mDAAAkhYZe_RrlHI4ZBuVk1zX0Q", "dfgd");
        System.out.println(result);
    }

    @Test
    public void getPreAuthCodeTest() {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getPreAuthCode(crmAppId);
        System.out.println(result);
    }

    @Test
    public void setSessionInfoTest() {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.setSessionInfo(crmAppId, "fdf");
        System.out.println(result);
    }

    @Test
    public void getDepartmentInfoTest() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> result = qyWeixinManager.getDepartmentInfo(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "2");
        System.out.println(result);
    }

    @Test
    public void getOrderInfoTest() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinOrderInfoRsp> result = qyWeixinManager.getOrderInfo(crmAppId, "N00000C661DC3641D437D6A22A4DE");
        System.out.println(result);
    }

    @Test
    public void getExternalContactListTest() {
        com.facishare.open.qywx.accountinner.result.Result<List<String>> result = qyWeixinManager.getExternalContactList("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        System.out.println(result);
    }

    @Test
    public void getUserInfoResultTest() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> result = qyWeixinManager.getUserInfoResult(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        System.out.println(result);
    }

    @Test
    public void getUserModelTest() {
        com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> result = qyWeixinManager.getUserModel(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        System.out.println(result);
    }

    @Test
    public void getDepInfoListTest() {
        com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> result = qyWeixinManager.getDepInfoList(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "1");
        System.out.println(result);
    }

    @Test
    public void getDepInfoTest() {
        com.facishare.open.qywx.accountinner.result.Result<OutDepModel> result = qyWeixinManager.getDepInfo(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "1");
        System.out.println(result);
    }

    @Test
    public void getDepEmpListTest() {
        com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> result = qyWeixinManager.getDepEmpList(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "1");
        System.out.println(result);
    }

    @Test
    public void getTagDetailTest() {
        com.facishare.open.qywx.accountinner.result.Result<TagDetailModel> result = qyWeixinManager.getTagDetail(repAppId, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "1");
        System.out.println(result);
    }

    @Test
    public void saveQyweixinTicketToken() {
        SuiteAuthXml authXml = new SuiteAuthXml();
        authXml.setSuiteId("dk97b554f2a62c74ff");
        authXml.setSuiteTicket("31iJ0NLZ0gRM5gHA0JtxdQYPZZY7zIeEvPynqlOxZIpFuORd7rgk8594jGbIe6pa");
        com.facishare.open.qywx.accountinner.result.Result<Void> result = qyWeixinManager.saveQyweixinTicketToken(authXml, Boolean.FALSE);
        System.out.println(result);
    }

    @Test
    public void getUserIdByPhone() {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getUserIdByPhone("wpwx1mDAAAsJuqgPFlcfGWtLOewfEiNw",
                "dk34d6bacd61e33d99",
                "***********");
        System.out.println(result);
    }

    @Test
    public void getRepUserInfoByCode() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinRepUserDetailInfoRsp> result = qyWeixinManager.getRepUserInfoByCode("dk34d6bacd61e33d99",
                "wpwx1mDAAAFtBrQCfiHtmRaUHt5hRo7A",
                "c7V3rflMmRpwGvBtBG3s-WNt34Q7Zxv3PNTEbgHTMSI");
        System.out.println(result);
    }

    @Test
    public void getAppLoginUserInfo() {
        com.facishare.open.qywx.accountinner.result.Result<Object> result = qyWeixinManager.getAppLoginUserInfo("5nWfumJYjdGPg-sthgkHLXJ0Wk2cg2e6WPuHioZqjqE",
                "dk97b554f2a62c74ff",
                "wwec96c36a003fcdde");
        System.out.println(result);
    }

    @Test
    public void getUserInfo3rd() {
        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserInfo3rdRsp> result = qyWeixinManager.getUserInfoByLoginAuthApp(
                "5-NcncvOTLze-iVjk-z534Np-ZPQChcrxIdqi00kjdo");
        System.out.println(result);
    }

    @Test
    public void listAppShareInfo() {
        com.facishare.open.qywx.accountinner.result.Result<List<ListAppShareInfoRsp.CorpInfo>> result = qyWeixinManager.listAppShareInfo(
                "wpwx1mDAAACigpu1Ro4u89tCMaZns7ag",
                "dk3ff8a65e707ca3c2",
                "1000002");
        System.out.println(result);
    }

    @Test
    public void getCorpGroupAccessToken() {
        com.facishare.open.qywx.accountinner.result.Result<String> result = qyWeixinManager.getCorpGroupAccessToken("wpwx1mDAAA0WQKpOLnXO6GcOzlxVY7ew",
                "dk3ff8a65e707ca3c2",
                "wpwx1mDAAAUf8zE0zlWW5t46SMWAmuEw",
                1000002,
                1);

        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfo2 = qyWeixinManager.getUserInfo("dk3ff8a65e707ca3c2",
                "wpwx1mDAAAUf8zE0zlWW5t46SMWAmuEw",
                "wowx1mDAAAxJPQlb6hF_9z4SAljDh6Xw");

        System.out.println(result);
    }
}
