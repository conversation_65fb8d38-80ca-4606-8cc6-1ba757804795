package com.facishare.open.qywx.accountsync.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.accountinner.result.Result;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.accountsync.dao.QyweixinOrderInfoDao;
import com.facishare.open.qywx.accountsync.manager.*;
import com.facishare.open.qywx.accountsync.model.CrmWechatInterfaceLicenseObj;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinOrderInfoBo;
import com.facishare.open.qywx.accountsync.utils.HttpHelper;
import com.facishare.open.qywx.accountsync.utils.xml.QyweixinAutoActiveMsgBaseXml;
import com.facishare.open.qywx.accountsync.utils.xml.XStreamUtils;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;
import java.util.Spliterator;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OrderManagerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;
    @Resource
    private QYWeixinManager qyWeixinManager;
    @Resource
    private OrderManager orderManager;
    @Autowired
    private CrmObjManager crmObjManager;
    @Autowired
    private CrmObjectSupportManager crmObjectSupportManager;
    @Autowired
    private QywxPermissionManager qywxPermissionManager;

    @Test
    public void test() {
        QyweixinOrderInfoBo qyweixinOrderInfoBo = new QyweixinOrderInfoBo();
        qyweixinOrderInfoBo.setOrderId("T00000C661DC35F5B2A74D361C4C4");
        List<QyweixinOrderInfoBo> list = qyweixinOrderInfoDao.findByEntity(qyweixinOrderInfoBo);
        QyweixinOrderInfoBo first = list.get(0);
        first.setOrderType(2);
        qyweixinOrderInfoDao.saveOrUpdateOrder(first);
        System.out.println("success");
    }

    @Test
    public void testAccount() {
        crmObjectSupportManager.createDefineObject(85836,"WechatInterfaceLicenseObj");
        Result<String> corpAccessToken = qyWeixinManager.getCorpAccessToken("dk97b554f2a62c74ff",
                "wpwx1mDAAApDuHDla66rUas9XUWu0rdg",
                true);
        //y7zU49W_idcf2ckMe6-Ob-_dKWrzR1BO_76M72uuDWuxM1fHNnjoELBPnrsZ0cCovK6y5HjhUwjrlMseo4y31BrQrT_vwruXmJ2xq82GXi9msQpmBH2Ov_Rnu77ZGfx-L6oUqx8fTVHkSROO8cVN7U_HGjBt2YlMc4YTcw-misxDydNgw1ssRh7eU8zaKA4_MymN-WiP9rnFCoOXvC-w4g
        System.out.println(corpAccessToken);
    }

    @Test
    public void testDept() {
        Result<String> corpAccessToken = qyWeixinManager.getCorpAccessToken("wx88a141937dd6f838", "ww986b47d6dead87f3", true);
        //y7zU49W_idcf2ckMe6-Ob-_dKWrzR1BO_76M72uuDWuxM1fHNnjoELBPnrsZ0cCovK6y5HjhUwjrlMseo4y31BrQrT_vwruXmJ2xq82GXi9msQpmBH2Ov_Rnu77ZGfx-L6oUqx8fTVHkSROO8cVN7U_HGjBt2YlMc4YTcw-misxDydNgw1ssRh7eU8zaKA4_MymN-WiP9rnFCoOXvC-w4g
        HttpHelper httpHelper = new HttpHelper();
        String url = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=" + corpAccessToken.getData();
        try {
            String result = httpHelper.doGet(url);
            System.out.println(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testGetExterContactList() {


        Result<String> corpAccessToken = qyWeixinManager.getCorpAccessToken("wx88a141937dd6f838", "ww986b47d6dead87f3", true);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=" + corpAccessToken.getData() + "&department_id=" + 4 + "&fetch_child=0";
        HttpHelper httpHelper = new HttpHelper();
//        try {
//            String result = httpHelper.doGet(url);
//            System.out.println(result);
//        } catch (RemoteException e) {
//            e.printStackTrace();
//        }

        String userId = "kny";
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list?access_token=" + corpAccessToken.getData() + "&userid=kny";
        try {
            String result = httpHelper.doGet(getContact);
            System.out.println(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void testLicense(){

        crmObjManager.batchCreateData(90896,null,Constant.WechatInterfaceLicenseObj);
       String orderId="******************************";
        QywxPermissionOrderDetail permitOrder =
                qyWeixinManager.getPermitOrder(orderId);
        logger.info(JSONObject.toJSONString(permitOrder));
    }

    @Test
    public void testListOrder(){
        String orderId="******************************";

        List<QywxPermissionOrderAccountDetail.OrderAccountList> permitOrderAccount = qyWeixinManager.getPermitOrderAccount(orderId);
        logger.info(JSONObject.toJSONString(permitOrderAccount));
    }

    @Test
    public void testProviderOrderList(){
        Long startTime=1728271188L;
        Long endTime=System.currentTimeMillis()/1000;
        Integer tenantId=84883;
        QywxPermissionOrderList orderList = qyWeixinManager.getPayOrderList(startTime, endTime, "wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");
        for (QywxPermissionOrderList.OrderList order : orderList.getOrder_list()) {
            String orderId = order.getOrder_id();
            QywxPermissionOrderDetail permitOrder = qyWeixinManager.getPermitOrder(orderId);
            List<QywxPermissionOrderAccountDetail.OrderAccountList> permitOrderAccount = qyWeixinManager.getPermitOrderAccount(orderId);
            List<ObjectData> createCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
            List<ObjectData> updateCrmWechatInterfaceLicenseObjs=Lists.newArrayList();
            //根据账号查询
            String corpId=permitOrder.getOrder().getOrder_id();
            List<List<QywxPermissionOrderAccountDetail.OrderAccountList>> partition = Lists.partition(permitOrderAccount, 100);//企业微信最多1000
            for (List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists : partition) {
                List<String> activeCodes = accountLists.stream().map(QywxPermissionOrderAccountDetail.OrderAccountList::getActive_code).collect(Collectors.toList());
                QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode(permitOrder.getOrder().getCorpid(), activeCodes);
                logger.info("permit order :{}"+JSONObject.toJSONString(activeInfoByCode));
                //批量创建
                //批量获取activeInfo的userid.查询关联企业微信员工id
                Map<String, String> crmobjDataMap = crmObjManager.queryByActiveCodes(tenantId, activeCodes, Constant.WechatInterfaceLicenseObj);

                for (QywxActiveCodeDetail.ActiveCodeInfo activeCodeInfo : activeInfoByCode.getActive_info_list()) {
                    ObjectData crmWechatInterfaceLicenseObj=new ObjectData();
                    crmWechatInterfaceLicenseObj.put("account_status",activeCodeInfo.getStatus());
                    crmWechatInterfaceLicenseObj.put("name",activeCodeInfo.getActive_code());
                    crmWechatInterfaceLicenseObj.put("account_type",activeCodeInfo.getType());
                    if(activeCodeInfo.getActive_time()!=null){
                        crmWechatInterfaceLicenseObj.put("active_time",activeCodeInfo.getActive_time()*1000);
                    }
                    if(activeCodeInfo.getExpire_time()!=null){
                        crmWechatInterfaceLicenseObj.put("expire_time",activeCodeInfo.getExpire_time()*1000);
                    }
                    crmWechatInterfaceLicenseObj.put("bind_wechat_userid",activeCodeInfo.getUserid());
                    if(activeCodeInfo.getMerge_info()!=null){
                        crmWechatInterfaceLicenseObj.put("to_active_code",activeCodeInfo.getMerge_info().getTo_active_code());
                        crmWechatInterfaceLicenseObj.put("from_active_code",activeCodeInfo.getMerge_info().getFrom_active_code());
                    }


                    crmWechatInterfaceLicenseObj.put("pay_time",permitOrder.getOrder().getPay_time()*1000);
                    crmWechatInterfaceLicenseObj.put("order_id",orderId);
                    crmWechatInterfaceLicenseObj.put("account_duration",permitOrder.calculateDuration());
                    crmWechatInterfaceLicenseObj.put("active_expire_time",permitOrder.calculateActiveExpireTime());
                    crmWechatInterfaceLicenseObj.put("owner",Lists.newArrayList("-10000"));
                    if(crmobjDataMap.get(activeCodeInfo.getActive_code())!=null){
                        //更新
                        String dataId = crmobjDataMap.get(activeCodeInfo.getActive_code());
                        crmWechatInterfaceLicenseObj.put("_id",dataId);
                        crmWechatInterfaceLicenseObj.remove("owner");
                        updateCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
                    }else{
                        //新增
                        createCrmWechatInterfaceLicenseObjs.add(crmWechatInterfaceLicenseObj);
                    }
                }
                if(CollectionUtils.isNotEmpty(createCrmWechatInterfaceLicenseObjs)){
                    crmObjManager.batchCreateData(tenantId,createCrmWechatInterfaceLicenseObjs, Constant.WechatInterfaceLicenseObj);
                }else {
                    crmObjManager.batchUpdateObject(tenantId,updateCrmWechatInterfaceLicenseObjs,Constant.WechatInterfaceLicenseObj);
                }

            }

            logger.info("permit order :{}"+JSONObject.toJSONString(permitOrderAccount));
        }
        logger.info("order list:{}"+JSONObject.toJSONString(orderList) );
    }
    @Test
    public void testQywxAccount(){
        QywxActiveCodeDetail activeInfoByCode = qyWeixinManager.getActiveInfoByCode("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", Lists.newArrayList("*************************"));



        String data="<xml><ServiceCorpId><![CDATA[wxbbe44d073ff6c715]]></ServiceCorpId><InfoType><![CDATA[auto_activate]]></InfoType><AuthCorpId><![CDATA[wpwx1mDAAAeCo4cNS8sBmAOyTOpr2M6w]]></AuthCorpId><Scene>2</Scene><TimeStamp>**********</TimeStamp><AccountList><ActiveCode><![CDATA[*************************]]></ActiveCode><Type>2</Type><ExpireTime>**********</ExpireTime><UserId><![CDATA[wowx1mDAAAu15FqMR0Qy0GMf6NoY5P2Q]]></UserId><PreviousStatus>1</PreviousStatus></AccountList></xml>";
        QyweixinAutoActiveMsgBaseXml qyweixinAutoActiveMsgBaseXml = XStreamUtils.parseXml(data, QyweixinAutoActiveMsgBaseXml.class);

        Long startTime=1728271188L;
        Long endTime=System.currentTimeMillis()/1000;
        String tenantId="84883";
        List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists = qyWeixinManager.getEnterpriseActiveAccount("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA");

        for (QywxPermissionOrderAccountDetail.OrderAccountList accountList : accountLists) {
            QywxActiveCodeDetail userAccountList = qyWeixinManager.getActiveInfoByUserId("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", accountList.getUserid());

        }
    }

    @Test
    public void getActiveInfoByUserId(){



        Long startTime=1728271188L;
        Long endTime=System.currentTimeMillis()/1000;
        String tenantId="84883";
        QywxActiveCodeDetail activeInfoByUserId = qyWeixinManager.getActiveInfoByUserId("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "wowx1mDAAAfPMspC9iKzWLx16d_zjcRA");
        logger.info("active message:{}"+JSONObject.toJSONString(activeInfoByUserId));
    }

    @Test
    public void testGetExternalList(){
        // {"errcode":0,"errmsg":"ok","external_userid":["wmwx1mDAAADUUP9FCu3h7PsyG6mG3rmg"]}
        String externalAccount="wowx1mDAAAZsPfjUCiEB9hdEfjw-82UA";
        Result<String> corpAccessToken = qyWeixinManager.getCorpAccessToken("wx88a141937dd6f838", "wpwx1mDAAAcy_L-ekn3ZBDU8-3CCvIjw", true);
        HttpHelper httpHelper = new HttpHelper();
        String url="https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token="+corpAccessToken.getData()+"&external_userid="+externalAccount;
        try {
            String result = httpHelper.doGet(url);
            System.out.println(result);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void testCorpIdList(){
        String qywxCorpId="wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA";
//        qywxPermissionManager.handlerAccountListByCorpId(qywxCorpId);
        long startTime=System.currentTimeMillis()/1000-2*24*60*60;
        qywxPermissionManager.handlerBetweenTime(null,startTime,System.currentTimeMillis()/1000);
    }

    @Test
    public void testCorpIdListActive(){
        String qywxCorpId="******************************";
        qywxPermissionManager.handlerOrderPaySuccess(qywxCorpId);
//        qywxPermissionManager.handlerBetweenTime(qywxCorpId,1727759596L,1730437996L);
    }

    @Test
    public void testCreateInterfaceOrder() {
        QyweixinOrderInfoRsp orderInfo = new QyweixinOrderInfoRsp();
        orderInfo.setPaid_corpid("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g");
        orderInfo.setPaid_time(1667288592L);
        orderInfo.setBegin_time(1667288592L);
        orderInfo.setEnd_time(1699861392L);
        orderInfo.setUser_count(30);
        orderInfo.setOrderid("interfaceOrderTest74860001");
        orderInfo.setOrder_type(2);

        //orderManager.createInterfaceOrder(orderInfo);
    }

    @Test
    public void openEnterprise() {
        String json = "{\"appId\":\"dk97b554f2a62c74ff\",\"avatar\":\"https://wx.qlogo.cn/mmhead/Q3auHgzwzM70FFCtbQEr5ScrBDBQGv4JicrxKRtyLVsuLibFHdtS4IdA/0\",\"corpId\":\"wpwx1mDAAApDuHDla66rUas9XUWu0rdg\",\"corpName\":\"贝贝CRM断网测试企业\",\"editionId\":\"sp4294bebe4b9a6ddc\",\"endTime\":\"2024/08/13\",\"orderType\":4,\"privilege\":{\"allowParty\":[],\"allowTag\":[],\"allowUser\":[]},\"userId\":\"wowx1mDAAATSS0q5k-pHl1r3LW-H8ePA\",\"userName\":\"U-FSQYWX-吴贝贝\"}";
        QyweixinEnterpriseOrder enterpriseOrder = JSONObject.parseObject(json,QyweixinEnterpriseOrder.class);
        orderManager.openEnterprise(enterpriseOrder);
    }

    @Test
    public void buyConnector() {
        Long startTime = System.currentTimeMillis();
        Long endTime = startTime + 1 * 365 * 24 * 60 * 60 * 1000L;
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = orderManager.buyConnector("81243",
                "0",
                startTime,
                endTime);
        System.out.println(result);
    }

    @Test
    public void judgeQywxConnectorModule() {
        ModuleFlag moduleFlag = orderManager.judgeQywxConnectorModule("81243");
        System.out.println(moduleFlag);
    }

    @Test
    public void queryFirstValidSCRMLicense() {
        ProductVersionPojo pojo = orderManager.queryFirstValidSCRMLicense("81243");
        System.out.println(pojo);
    }
}
