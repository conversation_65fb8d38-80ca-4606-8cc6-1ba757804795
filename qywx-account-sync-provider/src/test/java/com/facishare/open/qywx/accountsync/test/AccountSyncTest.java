package com.facishare.open.qywx.accountsync.test;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.oauth.result.GetFsUserIdsResult;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountsync.dao.QyweixinExternalContactTransferDao;
import com.facishare.open.qywx.accountsync.dao.QyweixinIdToOpenidDao;
import com.facishare.open.qywx.accountsync.manager.CorpManager;
import com.facishare.open.qywx.accountsync.manager.CrmObjManager;
import com.facishare.open.qywx.accountsync.manager.FsManager;
import com.facishare.open.qywx.accountsync.manager.QYWeixinManager;
import com.facishare.open.qywx.accountsync.model.GetFsUserIdsByRestResult;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactTransferBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinIdToOpenidBo;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.SuperAdminService;
import com.facishare.open.qywx.accountsync.service.impl.ContactBindInnerServiceImpl;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinAccountSyncServiceImpl;
import com.facishare.open.qywx.accountsync.service.impl.QyweixinGatewayInnerServiceImpl;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.save.vo.GenerateSettingVo;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeesDtoArg;
import com.facishare.organization.api.model.employee.result.GetAllEmployeesDtoResult;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by fengyh on 2018/3/3.
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")

public class AccountSyncTest extends AbstractJUnit4SpringContextTests {

    private static String source = "yunzhijia";
//    @Resource
//    private AppEaVisibleService appEaVisibleService;

    @Autowired
    ContactBindInnerServiceImpl contactBindInnerService;
    @Autowired
    EmployeeProviderService employeeProviderService;

    @Autowired
    private SuperAdminService superAdminService;
    private static EIEAConverter eIEAConverter;

    @Autowired
    private FsManager fsManager;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private QyweixinGatewayInnerServiceImpl qyweixinGatewayInnerService;

    @Autowired
    private QyweixinAccountSyncServiceImpl qyweixinAccountSyncService;

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private CorpManager corpManager;

    @Autowired
    private MessageGeneratingService generatingService;

    @Autowired
    private QyweixinExternalContactTransferDao qyweixinExternalContactTransferDao;

    @Resource
    private QyweixinIdToOpenidDao qyweixinIdToOpenidDao;
    @Autowired
    private CrmObjManager crmObjManager;

    @Test
    public void test3() throws IOException {
//        EmployeeDto employeeDto = OrganizationUtils.getEmployeeDto("74410", 1000);
//        System.out.println("-----------------------------------------");
//        System.out.println(employeeDto);
//        System.out.println("-----------------------------------------");
//        System.in.read();

        GetAllEmployeesDtoArg arg = new GetAllEmployeesDtoArg();
        arg.setEnterpriseId(74342);

        GetAllEmployeesDtoResult result =  employeeProviderService.getAllEmployees(arg);
        System.out.println();

    }

    private static int getEnterpriseId(String ea) {
        return eIEAConverter.enterpriseAccountToId(ea);
    }

    @Test
    public void  Test1() throws IOException {
        String employeeLine = contactBindInnerService.getEmployeeLine("81002", "ww986b47d6dead87f3");
        System.out.println("打印出employee员工信息 == " );
        System.out.println(employeeLine);
        System.in.read();

    }


    @Test
    public void testSuperAdmin() {
        Result<Integer> result = superAdminService.superInsertSql("insert into qyweixin_corp_bind (corp_id,corp_name,app_id,permanent_code,agent_id,status) values('corp_id100','corp_name','app_id','permanent_code_100','agent_id100',1);");
        superAdminService.superUpdateSql("update qyweixin_corp_bind set status=6 where corp_id='corp_id100'");
        superAdminService.superDeleteSql("delete from qyweixin_corp_bind where corp_id='corp_id100'");

        System.out.println(result);
    }

    @Test
    public void testFsManager() {
        GetFsUserIdsResult result = fsManager.getFsUserIdsBy("81961",1005);
        List<Integer> userId = Lists.newArrayList(1000,1001,1002);
        List<EmployeeDto> list = fsManager.getEmployeeInfos("81961",1000,userId);
        List<Integer> userList = fsManager.getUserIdsByDepartmentId("81961",1001);
        Map<Integer,String> allCircleMap = fsManager.getAllCircles("81961",1000);
        System.out.println(result);
    }

    @Test
    public void testFsManager2() {
        GetFsUserIdsByRestResult result = fsManager.getFsUserIdsByRestService("81961",1005);
        List<Integer> userId = Lists.newArrayList(99888);
        List<EmployeeDto> list = fsManager.getEmployeeInfos("88521",1000,userId);
        List<Integer> userList = fsManager.getUserIdsByDepartmentId("81961",1001);
        Map<Integer,String> allCircleMap = fsManager.getAllCircles("81961",1000);
        System.out.println(result);
    }

    @Test
    public void getEnterpriseData() {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseId(748601);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        System.out.println(result);
    }

    @Test
    public void getDomainByCorpId() {
        String domain = qyweixinGatewayInnerService.getDomainByCorpId("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g");
        System.out.println(domain);
    }

    @Test
    public void aut() {
        Result domain = qyweixinAccountSyncService.autRetention2("83838", 1, null);
        System.out.println(domain.getErrorCode());
    }

    @Test
    public void open() {
        Result domain = qyweixinAccountSyncService.openAuthorization2("83838", 1, null);
        System.out.println(domain.getErrorCode());
    }

    @Test
    public void autCorpBatch() {
        List<String> domain = qyweixinAccountSyncService.openAuthorizationByPage(1, 100);
        System.out.println(domain);
    }

    @Test
    public void autEmployeeId() {
        Map<String, String> d = qyweixinAccountSyncService.externalContactEmployeeId2("84883",null);
        Set<String> set = d.keySet();
        Iterator<String> iterator = set.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();//将下一位置的"真key"赋 给局部变量"假key"
            System.out.println(key + " " + d.get(key));
        }
        System.out.println();
    }
    @Test
    public void externalContact() {
        QyweixinContactInfo d = qyweixinAccountSyncService.externalContact2("84883",
                Lists.newArrayList( "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ"),
                "",
                100,
                null);
        System.out.println(d);
    }

    @Test
    public void externalContact4() {
        List<QyweixinExternalContactInfo> d = qyweixinAccountSyncService.queryExternalContactListTwoScheme("84883", "wowx1mDAAArLI4aPJXTqJx_UJCABHILQ");
        System.out.println(d);
    }

    @Test
    public void autEa() {
        GenerateSettingVo settingVo = new GenerateSettingVo();
        settingVo.setCorpSecret("9j-ZpOzHZuyXS48XPg8YpINz-D3FUoEGyi_TcP8BkgU");
        settingVo.setEa("83384");
        qyweixinAccountSyncService.queryExternalContactList(settingVo, 1000);
    }

    @Test
    public void externalContactBy() {//***********
        Map<String, String> d = qyweixinAccountSyncService.queryByPhone(83384, "tel", "***********", "AccountObj");
        System.out.println(d);
    }

    @Test
    public void externalContact1() {
        List<String> d = qyweixinAccountSyncService.getAutRetentionCorp();
        System.out.println(d);
    }

    @Test
    public void externalContact2() {
        int d = qyweixinAccountSyncService.getEiByEa("82236");
        System.out.println(d);
    }

    @Test
    public void getAuthorization2() {
        Result authorization = qyweixinAccountSyncService.getAuthorization2("84883",null);
        System.out.println(authorization);
    }

    @Test
    public void externalContact3() {
        String d = qyweixinAccountSyncService.getOutEaByFsEa("84883");
        System.out.println(d);
    }

    @Test
    public void externalContact5() {
        //Result<QyweixinCorpBindInfo> corpBindInfo = qyweixinAccountSyncService.getCorpBindInfo("ww7611fdf9bbbd3c67", "dkdf3684b6720635f7");
        Result<List<QyweixinExternalContactInfo>> qyweixinExternalContacts = qyweixinAccountSyncService.getQyweixinExternalContacts("wx88a141937dd6f838",
                "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g",null,
                "E.74860.4179",
                Lists.newArrayList("wmwx1mDAAA8k3XhgX9rJXxBzhYwWM9Jw"));
        System.out.println(qyweixinExternalContacts);
    }

    @Test
    public void sendAutoMessage() {
        AutoMessageArg autoMessageArg = new AutoMessageArg();
        autoMessageArg.setContent("U-FSQYWX-chenzongxin:[语音]\n" +
                "U-FSQYWX-chenzongxin:[图片]\n" +
                "U-FSQYWX-chenzongxin:你是个好人\n" +
                "U-FSQYWX-chenzongxin:你真的很好\n" +
                "U-FSQYWX-chenzongxin:你真的好\n" +
                "U-FSQYWX-chenzongxin:你很好\n" +
                "U-FSQYWX-chenzongxin:你好\n" +
                "U-FSQYWX-chenzongxin:[文档]\n" +
                "糕云:…\n" +
                "U-FSQYWX-chenzongxin:[语音]\n" +
                "U-FSQYWX-chenzongxin:[语音]\n" +
                "U-FSQYWX-chenzongxin:拉拉\n");
        autoMessageArg.setEa("83384");
        autoMessageArg.setEi(83384);
        autoMessageArg.setFsId("1000");
        Map<String, Object> map = new HashMap<>();
        List<String> list = new LinkedList<>();
        list.add("61a75444d1836400018df648");
        map.put("AccountObj", list);
        autoMessageArg.setRelatedObject(map);
        autoMessageArg.setTitle("U-FSQYWX-chenzongxin和糕云的聊天记录的聊天记录（外部）");
        autoMessageArg.setCreatorId("1000");
        autoMessageArg.setUrl("https://www.ceshi112.com/fsh5/chat/index.html#/mergedMessage/61cbdc5757b9d100013e8651");
        /**
         *
         *   "filename" -> "mmexport1640616507141.jpg.$.file.fileext"
         * "name" -> "mmexport1640616507141.jpg.$.file.fileext"
         * "create_time" -> {Long@12123} *************
         * "size" -> {Long@12121} 2243047
         * "path" -> "N_202112_28_e19be3140d244cd498374f532e42686f"
         * "ext" -> "$.file.fileext"
         *
         */
        Map<String, Object> attachments = new HashMap<>();
        List<Map<String, Object>> list1 = new LinkedList<>();
        Map<String, Object> attach1 = new HashMap<>();
        attach1.put("name", "4b6d424223d37ac80085741da8a9c809");
        attach1.put("path", "N_202112_28_9705ef8cdb9e494c98bfee6d459b11e8");
        attach1.put("size", "1467");
        attach1.put("filename", "mmexport1640616507141.jpg.$.file.fileext");
        attach1.put("create_time", "*************");
        attach1.put("ext", "$.file.fileext");
        list1.add(attach1);
        Map<String, Object> attach2 = new HashMap<>();
        attach2.put("name", "92d11292593d27698813d8fbfcdf3e5a.amr");
        attach2.put("path", "N_202112_29_914bdc759b5147059f116786f512d737");
        attach2.put("size", "944");
        attach2.put("filename", "92d11292593d27698813d8fbfcdf3e5a.amr");
        attach2.put("create_time", "*************");
        attach2.put("ext", "amr");
        list1.add(attach2);
        attachments.put("attachments", list1);
        qyweixinAccountSyncService.sendAutoMessage(autoMessageArg,attachments);
    }

    @Test
    public void saveSecretGenerating() {
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();

//        generateSettingVo.setEa("83387");
//        generateSettingVo.setFsTenantId(83387);
//        generateSettingVo.setQywxCorpId("12323");
        generateSettingVo.setCorpSecret("321654");
        String token = qyweixinAccountSyncService.getToken("82234", generateSettingVo.getCorpSecret());
//        com.facishare.open.qywx.save.result.Result<Integer> integerResult = generatingService.saveSecretSetting(generateSettingVo);
//        log.info("insert:{}",integerResult);
    }

    @Test
    public void testRoomHost() {

        QyweixinGroupChatDetail.GroupChat roomMessageId = qyweixinAccountSyncService.getRoomMessage2("84883", "wrwx1mDAAAEbHZ5rpY7Wsyr-xPEXVnfw",null);
        System.out.println(roomMessageId);
    }

    @Test
    public void testpEmployee() {

        qyweixinAccountSyncService.AutoGetExternalContactEmployeeId2("84883",null);
    }

    @Test
    public void testToken() {
        //String token = qyWeixinManager.getToken("KltFfP4gFE_gvTd7DUi0rJDPsCwH3Ikj9fhxmUGjWfk", "ww1dc427ba799b9ba6");
        com.facishare.open.qywx.accountinner.result.Result<String> token = qyWeixinManager.getToken("KltFfP4gFE_gvTd7DUi0rJDPsCwH3Ikj9fhxmUGjWfk", "ww1dc427ba799b9ba6");
        System.out.println(token);
    }

    @Test
    public void getFsAccountByOutAccount() {
        List<QyweixinAccountEmployeeMapping> account = qyweixinAccountSyncService.getFsAccountByOutAccount("wpwx1mDAAApDbzVUJyW8MaJ7k2a4vOUA",
                "wowx1mDAAA4QjYIsstXCAzhM_moWAeww",-1,null);
        System.out.println(account);
    }

    @Test
    public void queryEnterpriseReplaceApplication() {
        Integer integer = qyweixinAccountSyncService.queryEnterpriseReplaceApplication("ww7611fdf9bbbd3c67");
        integer = qyweixinAccountSyncService.queryEnterpriseReplaceApplication("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g");
        System.out.println(integer);
    }

    @Test
    public void getTagEmployeeListAndSave() {
        contactBindInnerService.getTagEmployeeListAndSave("wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g");
    }

    @Test
    public void getAllEmployeeListVisible2App() {
        List<QyweixinUserDetailInfoRsp> list = contactBindInnerService.getAllEmployeeListVisible2App("wpwx1mDAAAlHhsjfqdPRNWd0IzyMjWYw",
                "1");
        System.out.println(list);
    }

//    @Test
//    public void qywxCorpBind() {
//        Result<Void> result = qyweixinGatewayInnerService.qywxCorpBind("82777",
//                "wpwx1mDAAAH-IDsqaKUveaASb10BN06Q",
//                "年轻人不讲武德",
//                true,null);
//        System.out.println(result);
//    }

    @Test
    public void getExternalUserId() {
        Result<String> result = qyweixinGatewayInnerService.getExternalUserId("wmwx1mDAAAP5NISEHkE9K60MK4vEPZ-w");
        System.out.println(result);
    }

    @Test
    public void getQYWXCorpBindInfo() {
        String corpId = "ww7611fdf9bbbd3c67";
        String token = qyweixinAccountSyncService.getToken(corpId, "xI8zonXD0C1mniDbCliosss-x_DEFfG9tA8rY5Kd8w8");
        System.out.println(token);
    }

    @Test
    public void getSaveRepCorpInfoTask() {
        QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp = new QyweixinGetPermenantCodeRsp();
        QyweixinAuthCorpInfoRsp auth_corp_info = new QyweixinAuthCorpInfoRsp();
        auth_corp_info.setCorpid("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        qyweixinGetPermenantCodeRsp.setPermanent_code("GZbKS1I7VMuzsIkrBk2ajsuUxSSvJrb9H_B2pkD1bq0");
        QyweixinAuthInfoRsp auth_info = new QyweixinAuthInfoRsp();
        QyweixinAgentRsp qyweixinAgentRsp = new QyweixinAgentRsp();
        qyweixinAgentRsp.setAgentid(1000036);
        auth_info.setAgent(Lists.newArrayList(qyweixinAgentRsp));
        qyweixinGetPermenantCodeRsp.setAuth_corp_info(auth_corp_info);
        qyweixinGetPermenantCodeRsp.setAuth_info(auth_info);
        corpManager.saveRepCorpInfoTask(qyweixinGetPermenantCodeRsp, "dkdf3684b6720635f7");
    }

    @Test
    public void getSaveRepInfoTask() {
        QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp = new QyweixinGetPermenantCodeRsp();
        QyweixinAuthCorpInfoRsp auth_corp_info = new QyweixinAuthCorpInfoRsp();
        auth_corp_info.setCorpid("testRepSecret1");
        qyweixinGetPermenantCodeRsp.setPermanent_code("123456789101010101010");
        QyweixinAuthInfoRsp auth_info = new QyweixinAuthInfoRsp();
        QyweixinAgentRsp qyweixinAgentRsp = new QyweixinAgentRsp();
        qyweixinAgentRsp.setAgentid(*********);
        auth_info.setAgent(Lists.newArrayList(qyweixinAgentRsp));
        qyweixinGetPermenantCodeRsp.setAuth_corp_info(auth_corp_info);
        qyweixinGetPermenantCodeRsp.setAuth_info(auth_info);
        corpManager.saveRepInfoTask(qyweixinGetPermenantCodeRsp, "dkdf3684b6720635f7");
    }

    @Test
    public void getTestSaveSetting() {
         String ea = "84262";
        GenerateSettingVo generateSettingVo = new GenerateSettingVo();
        QyweixinAccountEnterpriseMapping result = new QyweixinAccountEnterpriseMapping();
        result.setIsvOutEa("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        result.setOutEa("wpwx1mDAAAZVdwgqOHLomWPL0J6rDUVA");
        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = generatingService.querySetting(ea, null, null);
        QyweixinCorpRep qywxCorpBindInfo = qyweixinAccountSyncService.getQYWXCorpBindInfo(result.getIsvOutEa());
        if (ObjectUtils.isNotEmpty(generateSettingVoResult.getData())) {
            if (ObjectUtils.isNotEmpty(qywxCorpBindInfo) && qywxCorpBindInfo.getPermanentCode().equals(generateSettingVoResult.getData().getCorpSecret())) {
                //使用代开发授权会话存档的企业
                generateSettingVo.setQywxCorpId(result.getIsvOutEa());
            } else {
                //使用自建应用授权会话存档的企业
                generateSettingVo.setQywxCorpId(result.getOutEa());
            }
        } else {
            generateSettingVo.setCorpSecret("-sup7-54sjEdbta1-wzYgVsZur-JBZYh0ZECNbDHzGw");
            generateSettingVo.setAgentId("1000036");
            generateSettingVo.setQywxCorpId(result.getIsvOutEa());
            String token = qyweixinAccountSyncService.getToken(generateSettingVo.getQywxCorpId(), generateSettingVo.getCorpSecret());
        }
        generateSettingVo.setEa(ea);
        generateSettingVo.setFsTenantId(84262);
        generateSettingVo.setSecret("ZWVYRYM11uaiG7xDtw0VUi2r67CXqcO0FPNkjO7P7ms");
        generateSettingVo.setPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCLrlqxctpv1dg8ERgfrsizH9s5I099XKGX0qFYi1NwKXKeXTL1bbWRMEWesHSMiNiQPGTouXRDXKmqyVnKJcMGgIBfMfO4uDQ8U7hUyYHvVRURP4uSOL2ZGXLwnIqlI7WRDtTU1RgKVomyDtKlOK9vtEc0js+hMT7OiriBy91XG67nClzzrYdew284Skx61Jc6CpC4hRxJjIABlq5RX5F+Wp5VzntykY/G6L+vdRu4KBnY+fxEowGmlDk2QxIA4K6S4OfXCYHHRmZanF8oWs+uBUTw1eLh90SEa/5ajwtxdA7HyDC8QVlKxerJClyqcpxv1ptjqFBBbXLvsVhwgXEhAgMBAAECggEATTUdmlIS3ZhFQsZsIC8bbq9gHJAhAvkttN7PIkM45plyaoi3fyOaJduZz+JXOcr2cZuAZ4cC9a0Fd4p+YBdJWpGy42uX/PWMof/gtrT/ZkwQLg2C11sXqcWAW/EbTbaUSM764326IRS+XbaFxp+zkToD1dBOghnXTpEs9Um7WcbxzllZfiJ3rd2UxswWvpB9tQk8li9NphY3ioVxckiCtvQiVnUZaT/0RKoae/hN3VPqG6Uv1ZiZ0kcOiu6KwOQ0nXHEN1uzga5UG87Y17V2JadiEUFWI/5O3KEhRf1rl8cNsod1gf9ObbBHH7AuJCYLvdbuXD3fPHWrHCueUXy9hQKBgQD3pY1uUmHZrzsPkIE3oN0Ls2bUcSZ1cIb6gN0zNOn08D0gSYOgJ76sF32cFtSnhqZueqdu4QvPzqTHeMF6oFh8KhdSVtVlWGDQVC8cczsAOq8wBdyckgSa5Pt7XG+g5kL5aaTDb+6XOqzsrKGdiyMpG8AefLYySIixP669JobaJwKBgQCQZIK8N9fNmP6QUNIUtmlZYdtG+LyUZKiCT3fSeYKZwNtfO2IoQYHJFoP4or+FdeUXVU9eaNaYqxp/VH93ZWbERgPwKWagZDoWQskuyfg3HVz3hZ3lc1CVUear8dtO1TTRissVNJnR+ki3jgsf+sBx0vLniLTZsje/KnCX0YBPdwKBgFv2hYPPYfjlgqgwAFw5B3z93RTNA/weknFaA0qtvqevwvNHeXKy77KWcpXRQJ0JeqqSL7UUKz+7PCO66xZvjwxk0Q5Joqsk26bhbDFDdUiLglzyAE/ARaeDmwPferCkcYCPQ5kz6sUMDAVDwixv69mrLXfk1f/sQZ6YyHoDYZaHAoGBAIXK/HDW9bnmSAsFOIREub0+tWY/2M1Pr+x/IjH+sYsybpMBfWR7vnzLxiE+/GP35/0E6XQ7hI0WDolpjGrfpKe9kKyaUCPSexhhbfVS5BJ9vMUGJFaV0Vdq+mjcxC9502VOS/ssMFOmrHaYwaoyONu/caAkxh7pyknyUz35vADlAoGBAOFTuI8DFp16a0L3+D1tnh/zWr5rsIjjxsKZ4Ov35dlLoKMfHYSIBlV8vDATkfO8KdJHeuncUoKJuGibgXI/dO90rAmxIGjTrd9p1YyEsoHqat9NCPz1aq7KNBxNHwmwu1XZI4JWSJwj4dx4emZGnqnx/sBgUASIIDedjwGUWnAT");
        generateSettingVo.setPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi65asXLab9XYPBEYH67Isx/bOSNPfVyhl9KhWItTcClynl0y9W21kTBFnrB0jIjYkDxk6Ll0Q1ypqslZyiXDBoCAXzHzuLg0PFO4VMmB71UVET+Lkji9mRly8JyKpSO1kQ7U1NUYClaJsg7SpTivb7RHNI7PoTE+zoq4gcvdVxuu5wpc862HXsNvOEpMetSXOgqQuIUcSYyAAZauUV+RflqeVc57cpGPxui/r3UbuCgZ2Pn8RKMBppQ5NkMSAOCukuDn1wmBx0ZmWpxfKFrPrgVE8NXi4fdEhGv+Wo8LcXQOx8gwvEFZSsXqyQpcqnKcb9abY6hQQW1y77FYcIFxIQIDAQAB");
        generateSettingVo.setVersion(5);
        generateSettingVo.setSeq(0);
        generateSettingVo.setAutRetention(1);
        com.facishare.open.qywx.save.result.Result<Integer> row = generatingService.    saveSetting(generateSettingVo);
        System.out.println(row);
    }

    @Test
    public void testPlainToEncryption() {
        qyweixinAccountSyncService.plainToEncryption("74860", Lists.newArrayList("XiongTao", "wmMRLhDAAAqyf9O5LC1l56y4grGc0cjw"));
    }

    @Test
    public void testQyweixinIdToOpenidDao() {
        int i1 = qyweixinIdToOpenidDao.deleteSwitchFailed();
        System.out.println(i1);
        QyweixinIdToOpenidBo a = new QyweixinIdToOpenidBo();
        a.setCorpId("test");
        List<QyweixinIdToOpenidBo> entity = qyweixinIdToOpenidDao.findByEntity(a);
        System.out.println(entity);

        a.setPlaintextId("1211");
        a.setOpenid("2311");
        a.setType(0);
        QyweixinIdToOpenidBo a1 = new QyweixinIdToOpenidBo();
        a1.setCorpId("test");
        a1.setPlaintextId("1211");
        a1.setType(1);
        int i = qyweixinIdToOpenidDao.batchSaveQyweixinIdToOpenid(Lists.newArrayList(a, a1));
        System.out.println(i);
    }


    @Test
    public void testSaveExternalContactTransfer() {
        for (int i = 0; i < 10; i++) {
            QyweixinTransferCustomerInfo customerInfo = new QyweixinTransferCustomerInfo();
            customerInfo.setEa("82777");
            customerInfo.setExternalUserId(Lists.newArrayList("wmwx1mDAAALOMXtj1JqpZNMbX-jVO6Yg"));
            customerInfo.setHandoverUserId("1009");
            customerInfo.setTakeoverUserId("1008");
            customerInfo.setHandoverDeptId("1");
            customerInfo.setHandoverDeptName("原");
            customerInfo.setTakeoverDeptId("2");
            customerInfo.setTakeoverDeptName("接");
            customerInfo.setExternalNickname("测试" + i + "@微信");
            customerInfo.setExternalUserName("测试" + i);
//            List<QyweixinTransferCustomerResult> customerResult = new LinkedList<>();
//            QyweixinTransferCustomerResult transferCustomerResult = new QyweixinTransferCustomerResult();
//            transferCustomerResult.setExternalUserId("wmwx1mDAAAzW5M5hH_pA3meNatfIRE4g");
//            transferCustomerResult.setErrCode(0);
//            customerResult.add(transferCustomerResult);
//        QyweixinTransferCustomerResult transferCustomerResult2 = new QyweixinTransferCustomerResult();
//        transferCustomerResult2.setExternalUserId("wmwx1mDAAAThwsjfmJMSTR8uaSxqKLHQ");
//        transferCustomerResult2.setErrCode(0);
//        customerResult.add(transferCustomerResult2);
//        QyweixinTransferCustomerResult transferCustomerResult3 = new QyweixinTransferCustomerResult();
//        transferCustomerResult3.setExternalUserId("wmwx1mDAAAZCJZ1Q6RTdses7Ml0hpKHA" );
//        transferCustomerResult3.setErrCode(12344);
//        customerResult.add(transferCustomerResult3);
            //qyweixinAccountSyncService.saveExternalContactTransfer(customerInfo, customerResult, "wpwx1mDAAAEkT4RFWyauSxOiN4Xb-R8g", 0);
        }
    }

    @Test
    public void testGetExternalContactTransferInfo() {
        QyweixinQueryTransferInfo transferInfo = new QyweixinQueryTransferInfo();
        transferInfo.setEa("74860");
        transferInfo.setHandoverUserStatus(-1);
        transferInfo.setSyncStatus(-1);
        transferInfo.setTransferResult(-1);
        //transferInfo.setTakeoverDeptName("化");
        //transferInfo.setExternalNickname("出");
        //transferInfo.setTransferTime("2022");
        //transferInfo.setExternalApiName("obj");
//        transferInfo.setStartTime("2022-08-10 19:35:51");
//        transferInfo.setEndTime("2022-08-11 19:35:51");
        Result<List<QyweixinExternalContactTransferInfo>> externalContactTransferInfo = qyweixinAccountSyncService.getExternalContactTransferInfo(transferInfo);
        System.out.println(externalContactTransferInfo.getData());
    }

    @Test
    public void testAuto() {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        Calendar c1 = Calendar.getInstance();
        c1.add(Calendar.DATE, -1);
        Date start = c1.getTime();
        String startTime= format1.format(start);//前一天

//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Calendar c = Calendar.getInstance();
//        c.add(Calendar.DATE, -1);
//        Date end = c.getTime();
//        String endTime= format.format(end);//前一天
        String endTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        List<QyweixinExternalContactTransferBo> handoverAndTakeover = qyweixinExternalContactTransferDao.getHandoverAndTakeover(startTime, endTime);
        System.out.println(handoverAndTakeover);
    }

    @Test
    public void testAutoTransferStatus() {
        qyweixinAccountSyncService.autoTransferStatus();
    }

    @Test
    public void checkEnterpriseProductVersion() {
        QyweixinEnterpriseLicenseModel model = new QyweixinEnterpriseLicenseModel();
        model.setFsEa("82777");
        model.setOutEa("wpwx1mDAAAEO7X_Roe5s3viyg0z_c06w");
        model.setCheckType("wechat_scrm");
        Result<Boolean> result = qyweixinAccountSyncService.checkEnterpriseProductVersion(model);
        System.out.println(result);
    }
    @Test
    public void testCrmObj(){
        ObjectData objectData=new ObjectData();
        objectData.put("name","QYWXTEST"+System.currentTimeMillis());
        objectData.put("account_status","1");
        objectData.put("active_time",System.currentTimeMillis());
        objectData.put("account_duration",36500);
        objectData.put("owner", Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
       crmObjManager.createObjData(84883,objectData,"WechatInterfaceLicenseObj");

    }

    @Test
    public void testUpdateObj(){
        ObjectData objectData=new ObjectData();
        String activeCode="QYWXTEST1730203089698";
        objectData.put("name",activeCode);
        objectData.put("account_type","1");
        crmObjManager.updateObjData(84883,activeCode,objectData,"WechatInterfaceLicenseObj");

    }

    @Test
    public void brushData(){

    }

}