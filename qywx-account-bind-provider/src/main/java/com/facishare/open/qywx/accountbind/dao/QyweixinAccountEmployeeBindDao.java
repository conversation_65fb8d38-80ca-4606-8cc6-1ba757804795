package com.facishare.open.qywx.accountbind.dao;

import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinEmployeeAccountModel;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by fengyh on 2018/3/1.
 */

@Repository
public interface QyweixinAccountEmployeeBindDao extends ICrudMapper<QyweixinAccountEmployeeMapping> {
    @Insert("<script>" + "insert ignore into employee_account_bind(source, fs_account, out_account, isv_account, app_id, out_ea, status," +
            "gmt_modified, gmt_create) " + "values " +
            "<foreach collection='accountMappingList' item='mapping' separator=','>" +
            "(#{mapping.source}, #{mapping.fsAccount}, #{mapping.outAccount}, #{mapping.isvAccount}, #{mapping.appId}, #{mapping.outEa}, #{mapping.status},now(), now())" + "</foreach>" + "</script>")
    int bindAccountEmployeeMapping(@Param("accountMappingList") List<QyweixinAccountEmployeeMapping> accountMappingList);

    @Select("<script>" + "select * from employee_account_bind where " + "source=#{source} and " + "out_ea=#{outEa} " +
            "<if test=\"appId != null and appId != ''\">and app_id = #{appId} </if> " +
            "<if test=\"outAccountList != null and outAccountList.size() > 0\"> and out_account in " +
            "<foreach collection='outAccountList' item='outAccount' " +
            "open='(' close=')'  separator=','>" + "#{outAccount}" + "</foreach></if>" + "</script>")
    List<QyweixinAccountEmployeeMapping> queryMappingFromOutAccountBatch(@Param("source") String source,
                                                                         @Param("appId") String appId,
                                                                         @Param("outAccountList") List<String> outAccountList,
                                                                         @Param("outEa") String outEa);

    @Select("<script>" + "select * from employee_account_bind where " + "source=#{source} and " + "fs_account in " +
            "<foreach collection='fsAccountList' item='fsAccount' open='(' close=')'  separator=','>" + "#{fsAccount" +
            "}" + "</foreach> <if test=\"appId != null and appId != ''\">and app_id = #{appId} </if>" +
            "<if test=\"status != -1\"> and status=#{status} </if> <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" + "</script>")
    List<QyweixinAccountEmployeeMapping> queryMappingFromFsAccountBatch(@Param("source") String source,
                                                                        @Param("appId") String appId,
                                                                        @Param("status") int status,
                                                                        @Param("fsAccountList") List<String> fsAccountList,
                                                                        @Param("outEa") String outEa);

    @Select("<script>" + "select * from employee_account_bind where " + "source=#{source} and " + "fs_account in " +
            "<foreach collection='fsAccountList' item='fsAccount' open='(' close=')'  separator=','>" + "#{fsAccount" +
            "}" + "</foreach> " +
            "<if test=\"status != -1\"> and status=#{status} </if> <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" + "</script>")
    List<QyweixinAccountEmployeeMapping> queryMappingFromFsAccount(@Param("source") String source,
                                                                   @Param("status") int status,
                                                                   @Param("fsAccountList") List<String> fsAccountList,
                                                                   @Param("outEa") String outEa);

    @Select("<script>" + "select * from employee_account_bind where source=#{source} and out_ea=#{outEa} and fs_account in " +
            "<foreach collection='fsAccountList' item='fsAccount' open='(' close=')'  separator=','>" + "#{fsAccount" +
            "}</foreach> <if test=\"status != -1\"> and status=#{status} </if>" + "</script>")
    List<QyweixinAccountEmployeeMapping> queryMappingWithFsAccounts(@Param("source") String source,
                                                                        @Param("outEa") String outEa,
                                                                        @Param("status") int status,
                                                                        @Param("fsAccountList") List<String> fsAccountList);

    @Update("<script>" + "update employee_account_bind set status=#{status} where source=#{source} and " +
            "fs_account like CONCAT('%',#{fsEa},'%') and app_id=#{appId} and " +
            "(out_account in <foreach collection='outAccounts' item='outAccount' open='(' close=')'  separator=','>#{outAccount}</foreach>" +
            "or isv_account in <foreach collection='outAccounts' item='outAccount' open='(' close=')'  separator=','>#{outAccount}</foreach>)" +
            "</script>")
    int changeEmployeeStatus(@Param("source")String source,
                               @Param("fsEa")String fsEa,
                               @Param("appId") String appId,
                               @Param("outAccounts") List<String> outAccounts,
                               @Param("status") int status);

    @Delete("<script>" + "delete from employee_account_bind where source=#{source} and fs_account=#{fsAccount} and app_id=#{appId} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if></script>")
    int deleteEmployeeMapping(@Param("source")String source,
                              @Param("fsAccount")String fsAccount,
                              @Param("appId")String appId,
                              @Param("outEa") String outEa);

    @Select("<script>" + "select * from employee_account_bind where fs_account like CONCAT(#{fsEa},'%') and app_id=#{appId} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" + "</script>")
    List<QyweixinAccountEmployeeMapping> queryAccountBindByFsEa(@Param("fsEa") String fsEa,
                                                                @Param("appId") String appId,
                                                                @Param("outEa") String outEa);

    @Select("<script>" + "select * from employee_account_bind where out_ea=#{outEa} and app_id=#{appId} and status=#{status}" + "</script>")
    List<QyweixinAccountEmployeeMapping> queryAccountBindByOutEa(@Param("outEa") String outEa,
                                                                 @Param("appId") String appId,
                                                                 @Param("status") int status);

    @Select("<script>" +
            "select * from employee_account_bind where out_ea=#{outEa} " +
            "and (out_account=#{outAccount} or isv_account=#{outAccount}) " +
            "<if test=\"status != -1\"> and status=#{status} </if>" +
            "<if test='fsEa != null and fsEa!=\"\"'> and fs_account like concat('%',#{fsEa},'%') </if>" +
            "</script>")
    List<QyweixinAccountEmployeeMapping> queryAccountBindList(@Param("outEa") String outEa,
                                                              @Param("outAccount") String outAccount,
                                                              @Param("status") int status,
                                                              @Param("fsEa") String fsEa);

    @Select("<script>" + "select isv_account from employee_account_bind where fs_account like concat('%',#{fsEa},'%') and app_id=#{appId} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" + "</script>")
    List<String> queryIsvAccountBindByFsEaOnlyOutAccount(@Param("fsEa") String fsEa,
                                                         @Param("appId") String appId,
                                                         @Param("outEa") String outEa);

    @Select("<script>" + "select * from employee_account_bind where source='qywx' and status=0 and fs_account = #{fsAccount} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> order by gmt_create ASC limit 1" + "</script>")
    QyweixinAccountEmployeeMapping getQywxEmployeeMapping(@Param("fsAccount") String fsAccount,
                                                          @Param("outEa") String outEa);
    @Select("<script>" + "select * from employee_account_bind where source='qywx' and fs_account = #{fsAccount} " +
            "<if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if> " +
            "and out_account = #{outAccount} " +
            "order by gmt_create ASC limit 1" + "</script>")
    QyweixinAccountEmployeeMapping getQywxEmployeeMapping2(@Param("fsAccount") String fsAccount,
                                                           @Param("outEa") String outEa,
                                                           @Param("outAccount") String outAccount);

    @Select("<script>" + "select * from employee_account_bind where source='qywx' and status=0 and fs_account like concat('%',#{fsEa},'%') and out_account = #{outAccount}" + "</script>")
    QyweixinAccountEmployeeMapping getFsEmployeeMapping(@Param("fsEa") String fsEa,
                                                        @Param("outAccount") String outAccount);

    @Select("<script>" + "select * from employee_account_bind where source='qywx' and status=0 and out_ea=#{outEa} and out_account = #{outAccount}" + "</script>")
    List<QyweixinAccountEmployeeMapping> getFsEmployeeMapping2(@Param("outEa") String outEa,
                                                         @Param("outAccount") String outAccount);

    @Select("<script>" + "select count(*) from employee_account_bind where fs_account like concat('%',#{fsEa},'%') and app_id=#{appId}" + "</script>")
    int countAccountBind(@Param("fsEa") String fsEa,@Param("appId") String appId);

    @Delete("<script>" + "delete from employee_account_bind where out_ea=#{outEa} and app_id=#{appId} " + "</script>")
    int deleteByOutEa(@Param("outEa") String outEa, @Param("appId") String appId);

    @Update("<script>" + "update employee_account_bind set out_account = #{newAccount} where" + " out_account" +
            "=#{oldAccount}  and out_ea=#{outEa} <if test=\"appId != null and appId != ''\">and app_id = #{appId} </if>"+ "</script>")
    int updateByNewOutAccount(@Param("newAccount")String newAccount,
                              @Param("oldAccount")String oldAccount,
                              @Param("appId")String appId,
                              @Param("outEa")String outEa);

    @Update("<script>" + "update employee_account_bind set isv_account = #{newAccount} where" + " out_account" +
            "=#{oldAccount}  and out_ea=#{outEa} <if test=\"appId != null and appId != ''\">and app_id = #{appId} </if>"+ "</script>")
    Integer updateAccountByIsv(@Param("newAccount") String newAccount,
                               @Param("oldAccount") String oldAccount,
                               @Param("appId") String appId,
                               @Param("outEa") String outEa);

//    @Select("<script>" + "select corp_id from qyweixin_corp_bind where " + " isv_corp_id=#{isvCorpId} and " + " app_id=#{appId}  " + "</script>")
//    String queryQyweixinCorpBindByIsv(@Param("isvCorpId") String isvCorpId, @Param("appId") String appId);

    @Update("<script>" + "update employee_account_bind set status = #{status} where fs_account=#{fsAccount} <if test=\"outEa != null and outEa != ''\">and out_ea = #{outEa} </if>" +"</script>")
    Integer updateEmployeeBindStatus(@Param("fsAccount") String fsAccount,
                                     @Param("status") int status,
                                     @Param("outEa")String outEa);

    @Update("<script>" + "update employee_account_bind set status=#{status} where " +
            "app_id=#{appId} and " +
            "fs_account in <foreach collection='fsAccountList' item='fsAccount' open='(' close=')'  separator=','>#{fsAccount}</foreach>" +
            "</script>")
    int batchUpdateFsEmpBindStatus(@Param("fsAccountList") List<String> fsAccountList,
                                   @Param("status") int status,
                                   @Param("appId") String appId,
                                   @Param("outEa")String outEa);

    @Delete("<script>" + "delete from employee_account_bind where app_id=#{appId} " +
            " and out_ea = #{corpId} " +
            " and fs_account like CONCAT('%',#{fsEa},'%') " +
            "</script>")
    int deleteEmployeeBind(@Param("corpId") String corpId, @Param("fsEa") String fsEa, @Param("appId")String appId);

    @Update("<script>" + "update employee_account_bind set out_account = #{accountMapping.outAccount}, isv_account = #{accountMapping.isvAccount}, status = #{accountMapping.status} where" + " fs_account" +
            "=#{accountMapping.fsAccount}  and out_ea=#{accountMapping.outEa} <if test=\"accountMapping.appId != null and accountMapping.appId != ''\">and app_id = #{accountMapping.appId} </if>"+ "</script>")
    Integer updateEmployeeBindMapping(@Param("accountMapping") QyweixinAccountEmployeeMapping accountMapping);

    @Select("<script>" + "SELECT en.fs_ea, en.out_ea, em.out_account\n" +
            "FROM employee_account_bind em\n" +
            "JOIN enterprise_account_bind en ON em.out_ea = en.out_ea\n" +
            "WHERE en.bind_type = 0 AND em.fs_account LIKE CONCAT('%', en.fs_ea, '%')\n" +
            "GROUP BY em.out_ea, em.out_account\n" +
            "HAVING COUNT(*) > 1" + "</script>")
    List<QyweixinEmployeeAccountModel> queryRepeatEmployees();
}
