<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!-- yun<PERSON><PERSON>a数据源 -->
    <bean id="fsOpenQywxDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="fs-open-qywx-db"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="fsOpenQywxDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.qywx.accountbind.model"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
    </bean>
    <!-- scan for mapper and let them be autowired -->
    <bean id="dbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.qywx.accountbind.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

    <!-- 数据同步事件 -->
<!--    <bean id="accountSyncDataSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg index="1" value="ACCOUNT_SYNC_DATA_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="ACCOUNT_SYNC_DATA_PROVIDER"/>-->
<!--        <constructor-arg index="3" value="ACCOUNT_SYNC_DATA_TOPIC"/>-->
<!--    </bean>-->

    <bean id="accountSyncDataSender" class="com.facishare.open.qywx.accountbind.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="FS_QYWX_SYNC_DATA_SECTION"/>
    </bean>

    <!-- 企业微信与CRM成功绑定事件 -->
<!--    <bean id="enterpriseAccountBindSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg index="1" value="ENTERPRISE_ACCOUNT_BIND_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="ENTERPRISE_ACCOUNT_BIND_PROVIDER"/>-->
<!--        <constructor-arg index="3" value="ENTERPRISE_ACCOUNT_BIND_TOPIC"/>-->
<!--    </bean>-->

    <bean id="enterpriseAccountBindSender" class="com.facishare.open.qywx.accountbind.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="FS_QYWX_CORP_BIND_SECTION"/>
    </bean>

    <!-- 企业微信账号和纷享销客账号绑定事件 -->
<!--    <bean id="employeeAccountBindSender" class="com.facishare.common.rocketmq.AutoConfRocketMQSender" init-method="init">-->
<!--        <constructor-arg index="0" value="fs-open-qywx-mq-cms"/>-->
<!--        <constructor-arg index="1" value="EMPLOYEE_ACCOUNT_BIND_NAMESERVER"/>-->
<!--        <constructor-arg index="2" value="EMPLOYEE_ACCOUNT_BIND_PROVIDER"/>-->
<!--        <constructor-arg index="3" value="EMPLOYEE_ACCOUNT_BIND_TOPIC"/>-->
<!--    </bean>-->

    <bean id="employeeAccountBindSender" class="com.facishare.open.qywx.accountbind.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX-EMPLOYEE-BIND-PROVIDER-SECTION"/>
    </bean>

    <bean id="outEventDataChangeMQSender" class="com.facishare.open.qywx.accountbind.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="out-event-bind-section"/>
    </bean>

    <bean id="oaconnectorEventDataChangeMQSender" class="com.facishare.open.qywx.accountbind.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="oaconnector-event-data-change-provider-section"/>
    </bean>

    <import resource="classpath:spring/ei-ea-converter.xml"/>

    <bean id="accountBindService" class="com.facishare.open.qywx.accountbind.service.impl.QyweixinAccountBindServiceImpl"/>
    <bean id="accountBindInnerService" class="com.facishare.open.qywx.accountbind.service.impl.QyweixinAccountBindServiceInnerImpl"/>

    <dubbo:application id="fsOpenQywxAccountBind" name="fs-open-qywx-accountbind"/>

    <dubbo:registry id="fsOpenQywxAccountBindRegistry" address="${dubbo.registry.address}"
                    file="${dubbo.registry.file}"/>
    <dubbo:protocol id="dubbo"
                    name="dubbo"
                    port="${duboo.port}"
                    threadpool="limited"
                    threads="300"
                    accepts="1500"
    />
    <dubbo:provider id="fsOpenQywxAccountBindProvider" application="fsOpenQywxAccountBind" protocol="dubbo"
                    registry="fsOpenQywxAccountBindRegistry" filter="tracerpc"/>

    <!--<dubbo:registry id="dubbo-registry-local" address="zookeeper://localhost:2181"/>-->
    <dubbo:service   interface="com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService"
                     ref="accountBindService"
                     timeout="20000"
                     protocol="dubbo"
                     version="1.0"
                     group="${dubboConfigGroup}"/>
    <dubbo:service   interface="com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService"
                     ref="accountBindInnerService"
                     timeout="20000"
                     protocol="dubbo"
                     version="1.0"
                     group="${dubboConfigGroup}"/>

    <dubbo:reference id="toolsService"
                     interface="com.facishare.open.qywx.accountinner.service.ToolsService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="30000"
                     check="false"
                     group="${dubboConfigGroup}"/>

    <dubbo:reference id="qyweixinGatewayInnerService"
                     interface="com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService"
                     protocol="dubbo"
                     version="1.0"
                     timeout="30000"
                     check="false"
                     group="${dubboConfigGroup}"/>

    <dubbo:reference id="enterpriseEditionService"
                     interface="com.facishare.uc.api.service.EnterpriseEditionService"
                     protocol="dubbo"
                     retries="0"/>

    <import resource="classpath:spring/qywx-i18n.xml"/>


    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- 启用@AspectJ注解 -->
    <aop:aspectj-autoproxy/>
    <context:component-scan base-package="com.facishare.open.qywx.accountbind,com.facishare.open.qywx.i18n"/>

    <!--aop打印log-->
    <bean id="logAspect" class="com.facishare.open.qywx.accountbind.aop.LogAspect"/>
    <aop:config>
        <aop:aspect id="logMonitor" ref="logAspect">
            <aop:pointcut id="monitor"
                          expression="(execution(* com.facishare.open.qywx.accountbind.service.impl.*.*(..))
                           or execution(* com.github.mybatis.provider.CrudProvider.*.*(..)))"/>
            <aop:around pointcut-ref="monitor" method="around"/>
        </aop:aspect>
    </aop:config>

</beans>