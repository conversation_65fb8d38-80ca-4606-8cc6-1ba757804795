<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-open-qywx-gateway</artifactId>
    <groupId>com.facishare.open</groupId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <packaging>war</packaging>

  <artifactId>qywx-message-save-provider</artifactId>

  <dependencies>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.16</version>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>com.facishare.open</groupId>-->
<!--      <artifactId>qywx-message-save-api</artifactId>-->
<!--      <version>1.0-SNAPSHOT</version>-->
<!--    </dependency>-->

<!--    <dependency>-->
<!--      <groupId>com.facishare.open</groupId>-->
<!--      <artifactId>qywx-account-inner-api</artifactId>-->
<!--      <version>1.0.1-SNAPSHOT</version>-->
<!--      <exclusions>-->
<!--        <exclusion>-->
<!--          <artifactId>javassist-3.14.0-GA</artifactId>-->
<!--          <groupId>org.ow2.util.bundles</groupId>-->
<!--        </exclusion>-->
<!--      </exclusions>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>qywx-account-bind-api</artifactId>
      <version>1.0.2-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare.open</groupId>
      <artifactId>qywx-account-sync-api</artifactId>
      <version>1.0.7-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aspects</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.5</version>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.3.4</version>
    </dependency>

    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>config-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.javassist</groupId>
          <artifactId>javassist</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!--升级版本-->
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>4.1.7.RELEASE</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>logconfig-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>metrics-oss</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>

<!--    文件服务器-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsc-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>protostuff-api</artifactId>
          <groupId>com.dyuproject.protostuff</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 企信Api -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-qixin-api</artifactId>
      <version>0.1.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-warehouse-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsi-proxy</artifactId>
    </dependency>

    <!--Mybatis查询分页-->
    <dependency>
      <groupId>com.github.pagehelper</groupId>
      <artifactId>pagehelper</artifactId>
      <version>5.1.6</version>
    </dependency>

    <!--xxl-job框架-->
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>fs-job-core</artifactId>
      <version>1.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-restful-common</artifactId>
    </dependency>

    <!-- id生成器 -->
    <dependency>
      <groupId>com.robert.vesta</groupId>
      <artifactId>vesta-service</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>com.facishare</groupId>-->
<!--      <artifactId>fs-qixin-api</artifactId>-->
<!--      <version>0.1.1-SNAPSHOT</version>-->
<!--    </dependency>-->
    <!--mq-->
<!--    <dependency>-->
<!--      <groupId>com.facishare</groupId>-->
<!--      <artifactId>fs-common-mq</artifactId>-->
<!--      <version>1.1.0-SNAPSHOT</version>-->
<!--      <scope>compile</scope>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>com.fxiaoke</groupId>-->
<!--      <artifactId>fs-rocketmq-support</artifactId>-->
<!--    </dependency>-->

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-crm-rest-api</artifactId>
      <version>2.0.4_erpdss-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>http-spring-support</artifactId>
      <version>3.4.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>rpc-trace</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--mongo start -->
    <dependency>
      <groupId>org.mongodb</groupId>
      <artifactId>mongo-java-driver</artifactId>
      <version>3.10.1</version>
    </dependency>
    <dependency>
      <groupId>org.mongodb.morphia</groupId>
      <artifactId>morphia</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>org.mongodb.morphia</groupId>
      <artifactId>morphia-logging-slf4j</artifactId>
      <version>1.0.0</version>
    </dependency>
    <!--mongo end -->

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mongo-spring-support</artifactId>
      <version>3.1.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-crypto</artifactId>
      <version>5.8.5</version>
    </dependency>

    <!--spock单元测试-->
    <dependency>
      <groupId>org.spockframework</groupId>
      <artifactId>spock-spring</artifactId>
      <version>1.3-groovy-2.4</version>
    </dependency>
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>qywx-message-save-api</artifactId>
          <version>1.0.1-SNAPSHOT</version>
          <scope>compile</scope>
      </dependency>
      <dependency>
          <groupId>com.facishare.open</groupId>
          <artifactId>qywx-account-inner-api</artifactId>
          <version>1.0.2-SNAPSHOT</version>
          <scope>compile</scope>
      </dependency>


  </dependencies>




  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>2.7</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <warName>qywx-message-save</warName>
        </configuration>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.dll</include>
          <include>**/*.so</include>
        </includes>
        <filtering>false</filtering>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>

  </build>

</project>
