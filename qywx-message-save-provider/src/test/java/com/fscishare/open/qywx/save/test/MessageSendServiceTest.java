package com.fscishare.open.qywx.save.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.qywx.save.manager.FileManager;
import com.facishare.open.qywx.save.model.ChatDatas;
import com.facishare.open.qywx.save.model.Qychat;
import com.facishare.open.qywx.save.utils.RSAUtil;

import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.exeception.IllegalImageException;
import com.facishare.qixin.api.model.message.content.Document;
import com.facishare.qixin.api.model.message.content.Image;
import com.facishare.stone.sdk.response.StoneFileImageProcessResponse;
import com.facishare.stone.sdk.response.StoneFileImageThumbnailResponse;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fscishare.open.qywx.save.test.BaseAbstractTest;
import com.tencent.wework.Finance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.*;
import java.util.List;
import java.util.Map;


/**
 * Created by fengyh on 2018/3/7.
 */


@Slf4j
public class MessageSendServiceTest extends BaseAbstractTest {


    @Autowired
    private FileManager fileManager;

    private static final String privKeyPEM = "-----BEGIN PRIVATE KEY-----\n"+
            "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDJNR8R3hyf+YwP75ybxgVHs8dejlWvzIo9p/WylMtIkNbSZFFJ3c2LxWbE2/BB47B/4LUYxBvWcvkRvRmj5666y3j7RD9hoYoeaLzI5qhj4l4grOMapSKWFRepRFNN/uPh470vbPreMhUxpM1ChPQN1UdMEaLphoQFJ7MJerPrOU3vu5QYfaN/crl/n1ewtQ110RqRBUm1nu5LRKMOkEXV5bpAHyhi86pdGxAqeBgQEe1MnoAvmPUfcHre3muDWYw9xl4wX4KpJu0n4LmwFSkoR5Atohlvf1yeQprI5fEpJ8kU+m+BFSVtMkAhN8oRxU4PYIsu4LBCAWDRgfsGVR63AgMBAAECggEBAI5jzIANin5f0JH6hP7sLRJoFMBCxDrr6izl7weZKx9IeO5dVfWLRQoUAb1w1F8crexhbmd/aR/jL4YxJ7MJo2rH0e8nhh8mfaM1hdw695HwQaKoFvPlR0uk06DTHI0Gw+g5DcpBOmwCT2NZeHUJt9kORS19EahnrIglZxtfPXJJUWriNqNlDkwtAyJIen20gv6DomrIJ61HFNKJU+pwIQS8TcOYGWDkl/8AUfUHwY25yh8wwTmX0zdwFOUokGflfUCX6nEsnx4MChV4ibFBHGCLByfN00VvzypkBjXYOiIcKLPYAepo9cXKDfchnPwnWzjJA0MpwoZHR0CyZiId2zECgYEA/EO9q+6nQH83SzDEkJedFcUZm3YUGG6Nw/RZsFMOltYMto4Scj1zItCWSnSmqocTGSE988uI2KsPP9MxdsXOCc9BmImIwxzj05YeqiXsJiEUkFm3BLAb6Z/S9Y+UrN9wa8wXLdeW9fj0yAPKhs3TJwN+R9QjAqbILui2UwB/yK8CgYEAzC/Wnrm3d4LPmHXs6rXarZIzP9uWxWcnlgVNVFsZOyV2hHdQmr20GdzlNTk9ODp1a9XfHR3UWZhmZTFpWkdsUQ6jzF/PfPh7M6iKRCqBE2bN7GvcOh1joi/Ca+9sGpWfQr7sZgSvajf1whKqZ34k54qBX/ZZCG72Mu4+1QiN/HkCgYAYTMoziqyvyNFhu9PjfcdS9oaN9CThaZzcWGhfVNDd9MaKu0rJmGPD4cXobC411Qcg75PRLTUEcg3o/wYPw+QiC8Xs1KrI6LqFgjt39mk2Dw+1C/9WQ0SdD5k5sFgJAwkISUOeVdsj3JRvw/W5YJBLfMmoT6YDtl8oLaCKhEzK3wKBgDDItTRTFtx86nB4rFQfgtG5fnkhU9JyJOkY9zLSWSLifoCDqURvUppjRngC5veKMAfFn3rrZ5LIcJ54wb0KF3z+THBF6+Ll0zmyaOaEaTZjd4um8YUJBIb5djAnkeKAIP7ncr+lGuv71sG5h/EWGGchlmuBBiCXskbU2To4wwOxAoGAErzONYsJIG73OSnq5cgfnf3hIodyCrDpIz57GcXE+VFd93k0A3KoiG54Z7ZOj6MtS8MHO31GgsPctVSUYuzlXRV7pdDhAm8jf24Wc6i7fav34UfHG+eFamO2X/ProgFRxOX5jQlaEDdWSodAMxkDHzDbhbMgXoUzH12Hn2cYhTI=\n"
            + "-----END PRIVATE KEY-----";


    @Test
    public void textSendMsg() throws Exception {
        Map<String, String> keyMap = RSAUtil.initKey();
        String publicKeyString = keyMap.get("publicKeyString");
        String privateKeyString = keyMap.get("privateKeyString");
        System.out.println(publicKeyString);
        System.out.println("-----------------------------------------------------");
        System.out.println(privateKeyString);
        log.info("HHHHH哈哈哈哈");

    }


    @Test
    public void initMessage() throws UnsupportedEncodingException {

        long sdk = Finance.NewSdk();
        Finance.Init(sdk, "wwbc77ff71db9569db", "ZWVYRYM11uaiG7xDtw0VUkO1aSs6_AvpoxKulwCRU5w");
        long slice = Finance.NewSlice();
        int ret = Finance.GetChatData(sdk, 79, 100, "", "", 600, slice);
        if (ret != 0) {
            return;
        } else {
            String content = Finance.GetContentFromSlice(slice);
            JSONObject jsonObject = JSONObject.parseObject(content);
            ChatDatas cdata = JSON.toJavaObject(jsonObject, ChatDatas.class);
            List<Qychat> list = cdata.getChatdata();
            for (Qychat ChatData : list) {
                System.out.println("msgid             :" + ChatData.getMsgid());
                System.out.println("Encrypt_random_key:" + ChatData.getEncrypt_random_key());
                String msgs = ChatData.getEncrypt_chat_msg();
                System.out.println("Encrypt_chat_msg  :" + msgs);
                String encrypt_key = null;
                try {
                    encrypt_key = RSAUtil.getPrivateKey(privKeyPEM,ChatData.getEncrypt_random_key());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                System.out.println("encrypt_key  :" + encrypt_key);
                long msg = Finance.NewSlice();
                System.out.println("result " + Finance.DecryptData(sdk, encrypt_key, msgs, msg));
                String datas = Finance.GetContentFromSlice(msg);
                log.info("{}",datas);
            }

        }

    }

    @Test
    public void initMediaData() {
//        long sdk = Finance.NewSdk();
//        Finance.Init(sdk, "wxbbe44d073ff6c715", "ELTTyb30Ny1AF3jUKaM6RNqQIQyCGAuEJSbsMelqp7M");
//        long ret = 0;
//        String sdkFileid = "CtQBMzA2ODAyMDEwMjA0NjEzMDVmMDIwMTAwMDIwNDIxZTYzYjY0MDIwMzBmNGRmYTAyMDQyMjcwZmIzYTAyMDQ2MDViZjE2ZTA0MjQ2NDYxMzM2MTM1NjM2MjM2MmQ2NDYxMzczMjJkMzQzMjMzMzYyZDYxNjYzNTM3MmQ2NTMzMzY2MjM0Mzg2MTMyNjQzNzYzMzQwMjAxMDAwMjAyMTFiMDA0MTBkZTRhMmMwYjU2NDliYzhiODZmOTk3ZDQ2Mjc0ZmM3MjAyMDEwMTAyMDEwMDA0MDASOE5EZGZNVFk0T0RnMU1EUXlPVEF3TURVME9GODNORE0yTmpjeU16QmZNVFl4TmpZek9ETXhPQT09GiAzNTYzMzUzNzM0MzczMDM0NjMzMzMxNjY2MzYxNjUzMg==";
//        String indexbuf = "";
//        //sdkFileid 是我们从第一步拉取下来的解密消息 然后通过第三步解密后 的消息内容中 获取到的值（text消息没有   只有文件  图片 语音 视频等消息才有此字段）
//        List<Byte> list = Lists.newArrayList();
//        while (true) {
//            long media_data = Finance.NewMediaData();
//            System.out.println(media_data + "--" + sdk);
//            ret = Finance.GetMediaData(sdk, indexbuf, sdkFileid, null, null, 3, media_data);
//            System.out.println("getmediadata ret:" + ret);
//            if (ret != 0) {
//                return;
//            }
//            System.out.printf("getmediadata outindex len:%d, data_len:%d, is_finis:%d\n",
//                    Finance.GetIndexLen(media_data), Finance.GetDataLen(media_data),
//                    Finance.IsMediaDataFinish(media_data));
//            try {
//                byte[] data = Finance.GetData(media_data);
//                list.addAll(Bytes.asList(data));
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            if (Finance.IsMediaDataFinish(media_data) == 1) {
//                // need free media_data
//                Finance.FreeMediaData(media_data);
//                break;
//            } else {
//                indexbuf = Finance.GetOutIndexBuf(media_data);
//                // need free media_data
//                Finance.FreeMediaData(media_data);
//            }
//        }
//        byte[] datas = Bytes.toArray(list);
//        InputStream inputStream = new BufferedInputStream(new ByteArrayInputStream(datas, 0, datas.length));
//        try {
//            UploadFileArg uploadFileArg=new UploadFileArg();
//            uploadFileArg.setFileName("111111212eee");
//            uploadFileArg.setMessageType("image");
//            uploadFileArg.setMessageId("1111111");
//            uploadFileArg.setEa("zhanghui0916");
//            UploadFileResult result = fileManager.uploadFile(inputStream, uploadFileArg);
//
//            byte[] bytes = fileManager.downloadNFile("zhanghui0916", result.getNpath());
//            FileOutputStream outputStream = new FileOutputStream(new File("D:\\CloudMusic\\aaqa.jpg"), true);
//            outputStream.write(bytes);
//            outputStream.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        //N_202107_27_7d607602f8a74eb48e8261da2853f2f0
        byte[] bytes = fileManager.downloadNFile("81961", "N_202107_27_7d607602f8a74eb48e8261da2853f2f0");
        log.info("bytes");

    }

    @Test
    public void TestFile() {
        long sdk = Finance.NewSdk();
        Finance.Init(sdk, "wxbbe44d073ff6c715", "ELTTyb30Ny1AF3jUKaM6RNqQIQyCGAuEJSbsMelqp7M");
        long ret = 0;
        String sdkFileid = "CpwCMzA4MThiMDIwMTAyMDQ4MTgzMzA4MTgwMDIwMTAwMDIwNDZiYjA3YzYxMDIwMzBmNGRmOTAyMDQ3M2U2NjA3MTAyMDQ2MDU0NTA3YTA0NDQ0ZTQ1NTc0OTQ0MzE1ZjM2NjI2MjMwMzc2MzM2MzEzNzMzNjUzNjM2MzAzNzMxMzYzMDM1MzQzNTMwNjYzMTVmMzU2MTM4MzM2NjM1NjMzOTJkNjQzNjYzMzQyZDM0MzM2MjYxMmQzODYzNjYzNzJkMzEzNDY0MzAzMjM5NjQzNzMwMzAzMDYyMDIwMTAwMDIwMzA4ZjBiMDA0MTAwZTA1ZjczMjNkOTI5NThkYjMxNjdjMDM5YTgyZWMxZTAyMDEwMjAyMDEwMDA0MDASOE5EZGZNVFk0T0RnMU1UWTJOams1TWpJeU5WOHhNemMwTURFeE1UVXdYekUyTVRZeE16ZzBPVFU9GiAyYTJjZjc5YjJmZDk0NThlYWQ4ZjE3NjY0ODdiMjJlMQ==";
        String indexbuf = "";
        //sdkFileid 是我们从第一步拉取下来的解密消息 然后通过第三步解密后 的消息内容中 获取到的值（text消息没有   只有文件  图片 语音 视频等消息才有此字段）


        while (true) {
            long media_data = Finance.NewMediaData();
            System.out.println(media_data + "--" + sdk);
            ret = Finance.GetMediaData(sdk, indexbuf, sdkFileid, null, null, 3, media_data);
            System.out.println("getmediadata ret:" + ret);
            if (ret != 0) {
                return;
            }
            System.out.printf("getmediadata outindex len:%d, data_len:%d, is_finis:%d\n",
                    Finance.GetIndexLen(media_data), Finance.GetDataLen(media_data),
                    Finance.IsMediaDataFinish(media_data));
            try {
                String fileName = "a.jpg";//文件名根据自己需要下载的文件类型自定义
                FileOutputStream outputStream = new FileOutputStream(new File("D:\\CloudMusic\\" + fileName), true);
                outputStream.write(Finance.GetData(media_data));
                outputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (Finance.IsMediaDataFinish(media_data) == 1) {
                // need free media_data
                Finance.FreeMediaData(media_data);
                break;
            } else {
                indexbuf = Finance.GetOutIndexBuf(media_data);
                // need free media_data
                Finance.FreeMediaData(media_data);
            }
        }


    }


    private String getFileName(JSONObject jsonObject, String fileType) {
        String fileName = null;
        String md5sum = jsonObject.getString("md5sum");
        switch (fileType) {
            case "image":
                fileName = md5sum + ".jpg";
                break;
            case "voice":
                fileName = md5sum + ".mp3";
                break;
            case "video":
                fileName = md5sum + ".mp4";
                break;
            case "file":
                fileName = jsonObject.getString("filename");
                break;
            default:
                fileName = "default.jpg";
                break;
        }
        return fileName;
    }


    @Test
    public void testMessage() {

        String privateKey = "MIIEowIBAAKCAQEAz/Hl7Ay0SE+zzdGMctJcQotAkCH2tT5/C4ISI4mR3N2KicE+\n" +
                "EdJCICxS6DrDVs6YOyvKR9XX2c31vy/qGM+rZxMjeIzHdQvFaY1cRPtVyLpAax++\n" +
                "3HnczJsFHV77evrK0GkaBNUtL5+05NTdBRcj66HY2aO/+4ttAk59hSS/8qtOGqm6\n" +
                "I5yfpQsKXE++8daQMTVUHMOA7aTmzCnn+FT9enXkjkcI/bCpgpEtBRh8plupIxXM\n" +
                "ui6PdT7RfGt5h0mWVHFRvlHC+N0bpYoNqzj43Y8AZSRBK8EP4TFP4Rlk7hAuFV4a\n" +
                "Ts3if5nv/63iMJ3YqG+y+PyEpZlnzfNCZE76SwIDAQABAoIBAHAzk940VKqX5urt\n" +
                "YJ0sCIAXZzTePqI5II/zFRp7xmqoV3JRBM7U5r05bVrFKlWSj+2NiU4NgrSRP0Jz\n" +
                "9hqBI3kwiHkpbQ4o1dJIZjsKapUuekfTD0cjshHsq2vXrlYDMKAXteRZqlICGLdI\n" +
                "bCGtBMLFx55XjuWJq74M4AmRdMjY0QSdnAgz+dcMtj3FxIn8VMfipV33FFobmRQC\n" +
                "YN6ItMW0uC9HXXNWQSwhaghmhSnm1EnxWFbny3mKY3qJnhasjUR6EJaFUSqN5I9Z\n" +
                "Nbz0VFFgjAxbtvfH7fqW9urEht1B61YGsK5o/+5BrTAY2+Vg0vgovs5hVRi3pCcM\n" +
                "APCg8IECgYEA5s6xnxDDDLS6bN0EoD0jfEfB1Nm8YNELtoQvpg9CbbYBnsgk0TFy\n" +
                "tQnKGkrfkkAP7AfVZIaflNyvqY959BRGt/AGVvaGuuLyufiEsYXsLW9Vd6mOmIYy\n" +
                "QEmoPUCQkn7HIezEvbxp3TTW7+fW6REQImkiUqrwx/fgR9axPzlj1aECgYEA5qRf\n" +
                "TlvlPlxLP6vFtbjDA2726Yvhy/d5nb5uxtHDwiaUsQENHfZoDkF6kN5FROsJp+7N\n" +
                "5Fr/cpYzPK51m2Wkgrvf8j+ImuMlKFD3XZPyVhT04TTNkP3xzeicKM+B/ZyzISc8\n" +
                "5Q5CYkfILF75TW6EDVDWkgvCxH8u0R+qx/z2sGsCgYAlQrECEN6sKnD+KiAZDkWw\n" +
                "RpVQG2aB6r2NVYGruULsGznfvEfVTbpK562s2PGG1ri7TfhxJhqVGZtyMCtr7+oK\n" +
                "v8EGQP43JXQx+aDSV+Bs5VBS5RiUHvX10u5KFSZBwB29qE+KoeQlReZ9DFxxe8Oz\n" +
                "Cm30EoyUe7vFXS94GXe4gQKBgQCjnh/uWsq5/odzV8weKkBOAz4uWAmKxLkF6r5z\n" +
                "VQPmi7AYEYLYqqEO2+yzMLs7NPHYrFRrlxJ4m40lky3jW6vlAprQI7opBtKpUybo\n" +
                "v7e+0YcW7HqYTU5ooIeHfA3feHarIkbUx9TYG8wpjgaVo70SJTLS0H0PIJp5yFlD\n" +
                "HnVymQKBgHyGRiQWCRiyrbamHcsWQlM0wj7+iIfyZxRLsMvr2BJ5C+1emn7uwSLB\n" +
                "E0tadmGdhvzHezn8xSfO9RWb4TWKqGvbwtVqAVBkKJV+EgtbQro+WWbTAP3eCb7l\n" +
                "ua0jXMGuJ2NwicusVr2YmkOId5QF8siEpEz/AERY0WviFi+qrINH";

        String randomKey = "m1dvasBvTRjKR/P9d5Xh68u4WJkT64YAVPQ3XAE8XUiM8CGG48fqqaABapAxuOVDIVmosndn0ssBamMhIhc7H7i7mkgH3/WTgpI5wgcpefSzwyTSp3sdbVOBJwlsJgOJ6JfvJH5qdof6F4eU9bESl4Xrl7tLXlKTcffTO9jzV/IfBim3iHhffIjU32z4yvC38Xz40W+4kgu467NdbxQegDIDFxVTThTkECQwRgwAuO6q1uIffZX7xJqMB6MWs4AOGXcwG7GlSmGjsgFDDnj8uWGoJTDTMfQzWqmJgH11psJ1EB1pWEiwD4TiHo/IY67t52GPvLzdfSx1AF94Lh8Shg==";
        String bytes = RSAUtil.decodeBase64(randomKey).toString();
        String content = "";
        try {
            content = RSAUtil.decryptByPriKey(bytes, privateKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(content);

    }


    private String buildContent(StoneFileUploadResponse result, String fileName, String messageType) {
        switch (messageType) {
            case MessageType.IMAGE:
                return createImage(result, fileName).toJson();
            case MessageType.DOCUMENT:
                return createDocument(result, fileName).toJson();
            default:
                return "WebImConstant.UNSUPPORTED_MSG_TYPE";
        }
    }

    private Image createImage(StoneFileUploadResponse response, String fileName) {
        Image image = new Image();
        image.setOriginalName(fileName);
        image.setFileSize(response.getSize());

        StoneFileImageProcessResponse imageProcessResponse = response.getImageProcessResponse();
        if (imageProcessResponse == null) {
            //获取图片列表异常

            return image;
        }
        List<StoneFileImageThumbnailResponse> thumbnailList = imageProcessResponse.getThumbnailList();

        //正常图片
        StoneFileImageThumbnailResponse normalImage = thumbnailList.get(2);
        image.setImage(buildNpath(normalImage.getPath(), normalImage.getExtensionName()));
        image.setImageW(normalImage.getWidth());
        image.setImageH(normalImage.getHeight());
        image.setImageSize(normalImage.getSize());
        //高清原图
        image.setHdImage(buildNpath(response.getPath(), response.getExtensionName()));
        image.setHdSize(response.getSize());
        //缩略图
        StoneFileImageThumbnailResponse smallThumbnail = thumbnailList.get(0);
        image.setThumbnail(buildNpath(smallThumbnail.getPath(), smallThumbnail.getExtensionName()));
        image.setThumbH(smallThumbnail.getHeight());
        image.setThumbW(smallThumbnail.getWidth());
        //中缩略图
        StoneFileImageThumbnailResponse middleThumbnail = thumbnailList.get(1);
        image.setHdThumbnail(buildNpath(middleThumbnail.getPath(), middleThumbnail.getExtensionName()));
        //小缩略图
        StoneFileImageThumbnailResponse tinyThumbnail = thumbnailList.get(3);
        image.setSmallThumbnail(buildNpath(tinyThumbnail.getPath(), tinyThumbnail.getExtensionName()));
        imageCheck(image);
        return image;
    }

    private Document createDocument(StoneFileUploadResponse result, String fileName) {
        Document document = new Document();
        document.setSize(result.getSize().intValue());
        document.setName(fileName);
        document.setFile(buildNpath(result.getPath(), result.getExtensionName()));
        return document;
    }

    private String buildNpath(String npath, String ext) {
        if (StringUtils.isNotBlank(ext)) {
            return npath + "." + ext;
        } else {
            return npath;
        }
    }

    private static void imageCheck(Image image) {
        if (image.getThumbW() <= 0 || image.getThumbH() <= 0) {
            throw new IllegalImageException("illegal image:" + image);
        }
    }


}
