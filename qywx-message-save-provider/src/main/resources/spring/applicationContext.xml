<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>

    <bean id="autoConf" class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="fs-open-qywx-message-save"/>
    <context:annotation-config/>
    <task:annotation-driven executor="taskExecutor"/>
    <context:component-scan base-package="com.facishare.open.qywx.save,com.facishare.open.qywx.i18n"/>
    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
    <import resource="dubbo-config.xml"/>
    <import resource="dubbo-provider.xml"/>
    <import resource="dubbo-consumer.xml"/>
    <import resource="spring-db.xml"/>
    <import resource="spring-job.xml"/>
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
    <import resource="classpath:/spring/spring-mq.xml"/>
    <import resource="classpath:spring/cus-crmrest.xml"/>
    <import resource="classpath:spring/mongo-store.xml"/>
    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>

    <import resource="classpath*:mongo/mongo-store.xml"/>

    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>


    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>


    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>
    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="messageService" class="com.facishare.dubbo.plugin.client.EISupportDubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.qixin.api.service.MessageService"/>
        <property name="serverHostProfile" ref="qixinApiHostProfile"/>
    </bean>


    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
    <aop:config>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:pointcut id="pointCutAround"
                          expression="(execution(* com.facishare.open.qywx.save.service.impl.*.*(..))) ||
                            (execution(* com.facishare.open.qywx.save.manager.*.*(..))) "/>
            <aop:around method="profile" pointcut-ref="pointCutAround"/>
        </aop:aspect>
    </aop:config>

    <!-- 日志拦截器 -->
    <bean id="logInterceptor" class="com.facishare.open.qywx.save.expection.LogInterceptor"/>

    <!-- 异常的统一处理 拦截器 -->
    <bean id="apiExceptionInterceptor" class="com.facishare.open.qywx.save.expection.ApiExceptionInterceptor"/>

    <bean id="crmRateLimiterAspect" class="com.facishare.open.qywx.save.expection.CrmRateLimiterAspect"/>

    <aop:config>
        <aop:pointcut id="serviceMethods"
                      expression="(execution(* com.facishare.open.qywx.save.service.impl.*.*(..))) ||
                            (execution(* com.facishare.open.qywx.save.manager.*.*(..))) and !(execution(* com.facishare.open.qywx.save.service.impl.MessageGeneratingServiceImpl.*(..))) and !(execution(* com.facishare.open.qywx.save.manager.QYWXFileManager.*(..)))"/>
        <aop:pointcut id="serviceAndManagerMethods"
                      expression="(execution(* com.facishare.open.qywx.save.service.impl.*.*(..))) ||
                            (execution(* com.facishare.open.qywx.save.manager.*.*(..))) and !(execution(* com.facishare.open.qywx.save.service.impl.MessageGeneratingServiceImpl.*(..))) and !(execution(* com.facishare.open.qywx.save.manager.QYWXFileManager.*(..)))  "/>
        <aop:pointcut id="crmRateLimiterMethods"
                      expression="(execution(* com.fxiaoke.crmrestapi.service.ObjectService.*(..)) or
                      execution(* com.fxiaoke.crmrestapi.service.ObjectDescribeService.*(..)) or
                      execution(* com.facishare.stone.sdk.StoneProxyApi.*(..)) or
                      execution(* com.facishare.fsi.proxy.service.NFileStorageService.*(..)) or
                      execution(* com.facishare.converter.EIEAConverter.*(..)) or
                      execution(* com.fxiaoke.crmrestapi.service.ObjectDataService.*(..)))"/>
        <aop:advisor order="0" pointcut-ref="serviceAndManagerMethods" advice-ref="logInterceptor"/>
        <aop:advisor order="1" pointcut-ref="serviceMethods" advice-ref="apiExceptionInterceptor"/>
        <aop:advisor order="2" pointcut-ref="crmRateLimiterMethods" advice-ref="crmRateLimiterAspect"/>
    </aop:config>

    <bean id="redisDataSource" class="com.facishare.open.qywx.save.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>

    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-open-qywx-redis-config"/>

    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

</beans>