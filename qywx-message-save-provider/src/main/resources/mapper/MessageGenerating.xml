<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.facishare.open.qywx.save.dao.MessageGeneratingDao">

    <resultMap id="baseResultMap" type="messageGeneratingPo">
        <result column="id" property="id" javaType="java.lang.Long"/>
        <result column="fs_tenant_id" property="fsTenantId" javaType="java.lang.Integer"/>
        <result column="ea" property="ea" javaType="java.lang.String"/>
        <result column="qywx_corp_id" property="qywxCorpId" javaType="java.lang.String"/>
        <result column="secret" property="secret" javaType="java.lang.String"/>
        <result column="public_key" property="publicKey" javaType="java.lang.String"/>
        <result column="private_key" property="privateKey" javaType="java.lang.String"/>
        <result column="version" property="version" javaType="java.lang.Integer"/>
        <result column="corp_secret" property="corpSecret" javaType="java.lang.String"/>
        <result column="agent_id" property="agentId" javaType="java.lang.String"/>
        <result column="storage_location" property="storageLocation" javaType="java.lang.String"/>
    </resultMap>

    <sql id="baseColumn">
        id, fs_tenant_id, ea, qywx_corp_id, secret,public_key,private_key,version,corp_secret, agent_id,storage_location
    </sql>


    <insert id="saveInfo" parameterType="messageGeneratingPo">
        insert  into message_generating (fs_tenant_id,ea,qywx_corp_id,secret,public_key,private_key,version,corp_secret,agent_id,storage_location)
        values (#{fsTenantId},#{ea},#{qywxCorpId},#{secret},#{publicKey},#{privateKey},#{version},#{corpSecret},#{agentId},#{storageLocation})
        ON DUPLICATE KEY UPDATE qywx_corp_id=#{qywxCorpId},secret=#{secret},corp_secret=#{corpSecret},storage_location=#{storageLocation}
    </insert>

    <select id="getByVersion" resultMap="baseResultMap">
        select <include refid="baseColumn"></include> from message_generating
        where ea=#{ea}
        <if test="version !=null">
            and version=#{version}
        </if>
        <if test="outEa !=null">
            and qywx_corp_id=#{outEa}
        </if>
        order by create_time desc limit 1;
    </select>
    <select id="queryAllEa" resultType="java.lang.String">
        select distinct ea
        from message_generating
    </select>
    <select id="queryByEaSetting" resultType="com.facishare.open.qywx.save.po.MessageGeneratingPo">
        select <include refid="baseColumn"></include>
        from message_generating
        where ea=#{ea}
    </select>

    <select id="getAuto" resultType="java.lang.Integer">
        select aut_retention
        from enterprise_account_bind
        where fs_ea=#{ea} <if test="outEa != null and outEa != ''">and out_ea = #{outEa} </if> order by gmt_create ASC limit 1
    </select>

    <update id="updateAuto">
        UPDATE enterprise_account_bind
        SET aut_retention=#{auto}
        WHERE fs_ea=#{ea} <if test="outEa != null and outEa != ''">and out_ea = #{outEa} </if>
    </update>

    <insert id="saveSecretInfo" parameterType="messageGeneratingPo">
        insert  into message_generating (fs_tenant_id,ea,qywx_corp_id,corp_secret,agent_id)
        values (#{fsTenantId},#{ea},#{qywxCorpId},#{corpSecret},#{agentId})
    </insert>

    <select id="queryAuto" resultType="java.lang.Integer">
        select count(*)
        from message_generating
        where ea=#{ea} and corp_secret is not null and version is null <if test="outEa != null and outEa != ''">and qywx_corp_id = #{outEa} </if>
    </select>

    <select id="queryIsAuto" resultType="java.lang.Integer">
        select count(*)
        from message_generating
        where ea=#{ea} <if test="outEa != null and outEa != ''">and qywx_corp_id = #{outEa} </if>
    </select>

    <update id="saveInfoByAuto" parameterType="messageGeneratingPo">
        UPDATE message_generating
        SET secret=#{secret},public_key=#{publicKey},private_key=#{privateKey},version=#{version}
        WHERE ea=#{ea} and fs_tenant_id=#{fsTenantId} and qywx_corp_id=#{qywxCorpId}
    </update>

    <update id="saveInfoByNoAuto" parameterType="messageGeneratingPo">
        UPDATE message_generating
        SET corp_secret=#{corpSecret},agent_id=#{agentId}
        WHERE ea=#{ea} and fs_tenant_id=#{fsTenantId} and qywx_corp_id=#{qywxCorpId}
    </update>

    <update id="updateCorpRepSecret" parameterType="messageGeneratingPo">
        UPDATE message_generating
        SET corp_secret=#{corpSecret},agent_id=#{agentId}
        WHERE qywx_corp_id=#{qywxCorpId}
    </update>

    <select id="getSecret" resultType="java.lang.String">
        select corp_secret
        from message_generating
        where ea=#{ea}
        order by create_time desc
    </select>

    <select id="querySecret" resultType="java.lang.Integer">
        select count(*)
        from message_generating
        where ea=#{ea} and corp_secret is not null and version is null
    </select>

    <select id="getAgentId" resultType="java.lang.String">
        select agent_id
        from message_generating
        where ea=#{ea}
        order by create_time desc
    </select>

    <update id="updateCorpSecret" parameterType="messageGeneratingPo">
        UPDATE message_generating
        SET corp_secret=#{corpSecret},secret=#{secret},public_key=#{publicKey},private_key=#{privateKey}
        WHERE ea=#{ea} and fs_tenant_id=#{fsTenantId} and qywx_corp_id=#{qywxCorpId} and version=#{version}
    </update>

</mapper>