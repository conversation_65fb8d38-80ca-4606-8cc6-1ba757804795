package com.facishare.open.qywx.save.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.facishare.open.qywx.save.arg.ConversionChangeArg;
import com.facishare.open.qywx.save.arg.MQArg;
import com.facishare.open.qywx.save.arg.MessageGeneratingArg;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.save.config.ConfigCenter;
import com.facishare.open.qywx.save.notify.AutoConfRocketMQProducer;
import com.github.trace.TraceContext;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class QiXinMsgSender {

    @Resource(name = "secretMessageSender")
    private AutoConfRocketMQProducer secretMessageSender;

    @Resource(name = "conversionSettingMessageSender")
    private AutoConfRocketMQProducer conversionSettingMessageSender;

    @Resource(name = "conversionChangeMessageSender")
    private AutoConfRocketMQProducer conversionChangeMessageSender;

    @Resource(name = "outEventDataChangeMQSender")
    private AutoConfRocketMQProducer outEventDataChangeMQSender;

    @Resource
    private EIEAConverter eieaConverter;

    public void sendToSecret(MQArg mqArg) {
        Message message = new Message();
        String body = JSONObject.toJSONString(mqArg);
        message.setBody(body.getBytes());
        if(ConfigCenter.TEM_CLOUD_EA.contains(mqArg.getEaList().get(0))) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.secretMessageSender.name());
            cloudMessageProxyProto.setFsEa(mqArg.getEaList().get(0));
            cloudMessageProxyProto.setMessage(message);
            //跨云
            this.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(mqArg.getEaList().get(0)), cloudMessageProxyProto);
        } else {
            SendResult sendResult = secretMessageSender.send(message);
            log.info("secretMessageSender sendToSecretProxy success. mqArg:{}, sendResult:{}", mqArg, sendResult);
        }
    }

    public void sendToConversionSetting(MessageGeneratingArg messageGeneratingArg, String tag) {
        Message message = new Message();
        String body = JSONObject.toJSONString(messageGeneratingArg);
        message.setBody(body.getBytes());
        message.setTags("tag_qywx_conversion_" + tag);
        if(ConfigCenter.TEM_CLOUD_EA.contains(messageGeneratingArg.getEa())) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.conversionSettingMessageSender.name());
            cloudMessageProxyProto.setFsEa(messageGeneratingArg.getEa());
            cloudMessageProxyProto.setMessage(message);
            //跨云
            this.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(messageGeneratingArg.getEa()), cloudMessageProxyProto);
        } else {
            SendResult sendResult = conversionSettingMessageSender.send(message);
            log.info("sendToConversionSetting sendToSecretProxy success. messageGeneratingArg:{}, sendResult:{}", messageGeneratingArg, sendResult);
        }
    }

    public void sendToConversionChange(ConversionChangeArg conversionChangeArg) {
        Message message = new Message();
        String body = JSONObject.toJSONString(conversionChangeArg);
        message.setBody(body.getBytes());
        if(ConfigCenter.TEM_CLOUD_EA.contains(conversionChangeArg.getEa())) {
            CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
            cloudMessageProxyProto.setType(CloudProxyEnum.conversionChangeMessageSender.name());
            cloudMessageProxyProto.setFsEa(conversionChangeArg.getEa());
            cloudMessageProxyProto.setMessage(message);
            //跨云
            this.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(conversionChangeArg.getEa()), cloudMessageProxyProto);
        } else {
            SendResult sendResult = conversionChangeMessageSender.send(message);
            log.info("conversionChangeMessageSender sendToSecretProxy success. mqArg:{}, sendResult:{}", conversionChangeArg, sendResult);
        }
    }

    public void sendCloudProxyMQ(Integer ei, CloudMessageProxyProto proto) {
        Message msg = new Message();
        msg.setTags("cloud_proxy_event_tag");
        msg.setBody(proto.toProto());

        //把纷享云的MQ投递到所有的专属云
        TraceContext context = TraceContext.get();
        context.setEi(String.valueOf(ei));

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("conversionChangeMessageSender.sendCloudProxyMQ,sendResult={}",sendResult);
    }

}
