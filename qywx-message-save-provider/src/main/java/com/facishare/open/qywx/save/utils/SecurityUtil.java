package com.facishare.open.qywx.save.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import com.facishare.open.qywx.accountinner.annotation.SecurityField;
import com.facishare.open.qywx.save.config.ConfigCenter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/4/23 17:38
 * @Version 1.0
 */
@Slf4j
public class SecurityUtil {
    /**
     * aes用来加密解密的byte[]
     */
    private final static byte[] SECRET_BYTES = Base64.decode(ConfigCenter.BASE64_SECRET);

    /**
     * 根据这个秘钥得到一个aes对象
     */
    @Getter
    private final static AES aes = SecureUtil.aes(SECRET_BYTES);

    /**
     * 对字符串进行加密处理
     * @param str
     * @return
     * @throws Exception
     */
    public static String encryptStr(String str) {
        if(StringUtils.isEmpty(str)) {
            return str;
        }
        //加密  这里我使用自定义的AES加密工具、
        String encryptValue=str;
        try {
            encryptValue = aes.encryptBase64(str);
        } catch (Exception e) {
            log.debug("encrypt value fail:{}",e.getMessage());
        }
        return encryptValue;
    }

    /**
     * 对字符串进行解密处理
     * @param str
     * @return
     * @throws Exception
     */
    public static String decryptStr(String str) {
        if(StringUtils.isEmpty(str)) {
            return str;
        }
        //解密
        String decryptValue=str;
        try {
            decryptValue = aes.decryptStr(str);
        } catch (Exception e) {
            log.debug("decrypt value fail:{}",e.getMessage());
        }
        return decryptValue;
    }

    /**
     * 加密
     *
     * @param declaredFields paramsObject所声明的字段
     * @param paramsObject   mapper中paramsType的实例
     * @return T
     * @throws IllegalAccessException 字段不可访问异常
     */
   public static  <T> T encrypt(Field[] declaredFields, T paramsObject) throws Exception {
        for (Field field : declaredFields) {
            SecurityField sensitiveField = field.getAnnotation(SecurityField.class);
            if (!Objects.isNull(sensitiveField)) {
                field.setAccessible(true);
                Object object = field.get(paramsObject);
                //暂时只实现String类型的加密
                if (ObjectUtils.isNotEmpty(object) && object instanceof String) {
                    //加密  这里我使用自定义的AES加密工具、
                    String encryptValue=object.toString();
                    try {
                        encryptValue = aes.encryptBase64(object.toString());
                    } catch (Exception e) {
                        log.debug("encrypt value fail:{}",e.getMessage());
                    }
                    field.set(paramsObject, encryptValue);
                    System.out.println(field);
                }
            }
        }
        return paramsObject;
    }

    /**
     * 解密
     *
     * @param result resultType的实例
     * @return T
     * @throws IllegalAccessException 字段不可访问异常
     */
    public static  <T> T decrypt(T result) throws IllegalAccessException {
        //取出resultType的类
        Class<?> resultClass = result.getClass();
        Field[] declaredFields = resultClass.getDeclaredFields();
        for (Field field : declaredFields) {
            SecurityField sensitiveField = field.getAnnotation(SecurityField.class);
            if (!Objects.isNull(sensitiveField)) {
                field.setAccessible(true);
                Object object = field.get(result);
                //暂时只实现String类型的加密
                if (ObjectUtils.isNotEmpty(object) && object instanceof String) {
                    //解密
                    String encryptValue=object.toString();
                    try {
                        encryptValue = aes.decryptStr(object.toString());
                    } catch (Exception e) {
                        log.debug("decrypt value fail:{}",e.getMessage());
                    }
                    field.set(result, encryptValue);
                    System.out.println(field);
                }
            }
        }
        return result;
    }
}
