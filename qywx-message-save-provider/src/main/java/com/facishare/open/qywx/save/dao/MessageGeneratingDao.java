package com.facishare.open.qywx.save.dao;

import com.facishare.open.qywx.save.po.MessageGeneratingPo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:18
 * @Version 1.0
 */
@Repository
public interface MessageGeneratingDao {

    int saveInfo(MessageGeneratingPo messageGeneratingPo);

    MessageGeneratingPo getByVersion(@Param("ea") String ea,
                                     @Param("version") Integer version,
                                     @Param("outEa") String outEa);

    List<String> queryAllEa();

    List<MessageGeneratingPo> queryByEaSetting(@Param("ea") String ea);

    Integer getAuto(@Param("ea") String ea,
                    @Param("outEa") String outEa);

    int updateAuto(@Param("ea") String ea,
                   @Param("auto") int auto,
                   @Param("outEa") String outEa);

    int saveSecretInfo(MessageGeneratingPo messageGeneratingPo);

    //secret有值，查询第一次插入自动留存
    int queryAuto(@Param("ea") String ea,
                  @Param("outEa") String outEa);

    int queryIsAuto(@Param("ea") String ea,
                    @Param("outEa") String outEa);

    int updateCorpRepSecret(MessageGeneratingPo messageGeneratingPo);

    //secret有值，更新第一次插入自动留存
    int saveInfoByAuto(MessageGeneratingPo messageGeneratingPo);

    int saveInfoByNoAuto(MessageGeneratingPo messageGeneratingPo);

    List<String> getSecret(MessageGeneratingPo messageGeneratingPo);

    List<String> getAgentId(MessageGeneratingPo messageGeneratingPo);

    int updateCorpSecret(MessageGeneratingPo messageGeneratingPo);
}
